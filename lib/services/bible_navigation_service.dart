import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum NavigationContext {
  appStartup,
  tabSwitch,
  backNavigation,
  directNavigation,
  deepLink,
}

class BibleLocation {
  final int bookId;
  final String bookName;
  final int chapter;
  final double scrollPosition;
  final DateTime timestamp;

  BibleLocation({
    required this.bookId,
    required this.bookName,
    required this.chapter,
    this.scrollPosition = 0.0,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'bookId': bookId,
    'bookName': bookName,
    'chapter': chapter,
    'scrollPosition': scrollPosition,
    'timestamp': timestamp.toIso8601String(),
  };

  factory BibleLocation.fromJson(Map<String, dynamic> json) => BibleLocation(
    bookId: json['bookId'],
    bookName: json['bookName'],
    chapter: json['chapter'],
    scrollPosition: json['scrollPosition'] ?? 0.0,
    timestamp: DateTime.parse(json['timestamp']),
  );

  @override
  bool operator ==(Object other) =>
    identical(this, other) ||
    other is BibleLocation &&
    runtimeType == other.runtimeType &&
    bookId == other.bookId &&
    chapter == other.chapter;

  @override
  int get hashCode => bookId.hashCode ^ chapter.hashCode;
}

class NavigationSession {
  final String sessionId;
  final DateTime startTime;
  final List<BibleLocation> visitedLocations;
  final NavigationContext context;

  NavigationSession({
    required this.sessionId,
    required this.startTime,
    required this.visitedLocations,
    required this.context,
  });

  Map<String, dynamic> toJson() => {
    'sessionId': sessionId,
    'startTime': startTime.toIso8601String(),
    'visitedLocations': visitedLocations.map((l) => l.toJson()).toList(),
    'context': context.name,
  };

  factory NavigationSession.fromJson(Map<String, dynamic> json) => NavigationSession(
    sessionId: json['sessionId'],
    startTime: DateTime.parse(json['startTime']),
    visitedLocations: (json['visitedLocations'] as List)
        .map((l) => BibleLocation.fromJson(l))
        .toList(),
    context: NavigationContext.values.firstWhere(
      (c) => c.name == json['context'],
      orElse: () => NavigationContext.directNavigation,
    ),
  );
}

class BibleNavigationService extends ChangeNotifier {
  static const String _currentLocationKey = 'bible_current_location';
  static const String _readingHistoryKey = 'bible_reading_history';
  static const String _navigationSessionKey = 'bible_navigation_session';
  static const String _userPreferencesKey = 'bible_user_preferences';
  static const String _lastAppStartKey = 'bible_last_app_start';

  BibleLocation? _currentLocation;
  List<BibleLocation> _readingHistory = [];
  NavigationSession? _currentSession;
  Map<String, dynamic> _userPreferences = {};
  bool _isInitialized = false;
  DateTime? _lastAppStart;
  bool _disposed = false;

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  @override
  void notifyListeners() {
    if (!_disposed) {
      super.notifyListeners();
    }
  }

  // Getters
  BibleLocation? get currentLocation => _currentLocation;
  List<BibleLocation> get readingHistory => List.unmodifiable(_readingHistory);
  NavigationSession? get currentSession => _currentSession;
  bool get isInitialized => _isInitialized;
  bool get shouldAutoRestore => _userPreferences['autoRestore'] ?? true;
  int get maxHistoryItems => _userPreferences['maxHistoryItems'] ?? 50;

  Future<void> initialize() async {
    if (_isInitialized) return;

    final prefs = await SharedPreferences.getInstance();
    
    // Load current location
    final locationData = prefs.getString(_currentLocationKey);
    if (locationData != null) {
      try {
        _currentLocation = BibleLocation.fromJson(jsonDecode(locationData));
      } catch (e) {
        debugPrint('Error loading current location: $e');
      }
    }

    // Load reading history
    final historyData = prefs.getString(_readingHistoryKey);
    if (historyData != null) {
      try {
        final historyList = jsonDecode(historyData) as List;
        _readingHistory = historyList
            .map((item) => BibleLocation.fromJson(item))
            .toList();
      } catch (e) {
        debugPrint('Error loading reading history: $e');
      }
    }

    // Load user preferences
    final preferencesData = prefs.getString(_userPreferencesKey);
    if (preferencesData != null) {
      try {
        _userPreferences = jsonDecode(preferencesData);
      } catch (e) {
        debugPrint('Error loading user preferences: $e');
      }
    }

    // Load last app start time
    final lastAppStartString = prefs.getString(_lastAppStartKey);
    if (lastAppStartString != null) {
      _lastAppStart = DateTime.parse(lastAppStartString);
    }

    // Update last app start time
    final now = DateTime.now();
    await prefs.setString(_lastAppStartKey, now.toIso8601String());
    
    _isInitialized = true;
    notifyListeners();
  }

  Future<void> updateCurrentLocation(
    BibleLocation location, {
    NavigationContext context = NavigationContext.directNavigation,
    bool addToHistory = true,
  }) async {
    _currentLocation = location;

    if (addToHistory) {
      _addToHistory(location);
    }

    await _persistCurrentLocation();
    await _persistHistory();

    // Update current session
    if (_currentSession != null) {
      _currentSession!.visitedLocations.add(location);
    }

    notifyListeners();
  }

  Future<void> startNavigationSession(NavigationContext context) async {
    final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
    _currentSession = NavigationSession(
      sessionId: sessionId,
      startTime: DateTime.now(),
      visitedLocations: _currentLocation != null ? [_currentLocation!] : [],
      context: context,
    );

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_navigationSessionKey, jsonEncode(_currentSession!.toJson()));
  }

  void _addToHistory(BibleLocation location) {
    // Remove if already exists to avoid duplicates
    _readingHistory.removeWhere((l) => l == location);
    
    // Add to beginning
    _readingHistory.insert(0, location);
    
    // Limit history size
    if (_readingHistory.length > maxHistoryItems) {
      _readingHistory = _readingHistory.take(maxHistoryItems).toList();
    }
  }

  Future<void> _persistCurrentLocation() async {
    if (_currentLocation == null) return;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentLocationKey, jsonEncode(_currentLocation!.toJson()));
  }

  Future<void> _persistHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = _readingHistory.map((l) => l.toJson()).toList();
    await prefs.setString(_readingHistoryKey, jsonEncode(historyJson));
  }

  bool shouldAutoRestoreForContext(NavigationContext context) {
    if (!shouldAutoRestore) return false;

    // Check if this is a fresh app start (more than 5 minutes since last start)
    if (_lastAppStart != null) {
      final timeSinceLastStart = DateTime.now().difference(_lastAppStart!);
      final isFreshStart = timeSinceLastStart.inMinutes > 5;
      
      switch (context) {
        case NavigationContext.appStartup:
          return isFreshStart;
        case NavigationContext.tabSwitch:
          return isFreshStart;
        case NavigationContext.backNavigation:
          return false; // Never auto-restore on back navigation
        case NavigationContext.directNavigation:
          return isFreshStart;
        case NavigationContext.deepLink:
          return true;
      }
    }

    return context == NavigationContext.appStartup || context == NavigationContext.deepLink;
  }

  Future<void> updateUserPreferences(Map<String, dynamic> preferences) async {
    _userPreferences.addAll(preferences);
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userPreferencesKey, jsonEncode(_userPreferences));
    
    notifyListeners();
  }

  Future<void> clearHistory() async {
    _readingHistory.clear();
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_readingHistoryKey);
    
    notifyListeners();
  }

  Future<void> clearCurrentLocation() async {
    _currentLocation = null;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentLocationKey);
    
    notifyListeners();
  }

  List<BibleLocation> getRecentBooks({int limit = 5}) {
    final Map<int, BibleLocation> uniqueBooks = {};
    
    for (final location in _readingHistory) {
      if (!uniqueBooks.containsKey(location.bookId)) {
        uniqueBooks[location.bookId] = location;
      }
      if (uniqueBooks.length >= limit) break;
    }
    
    return uniqueBooks.values.toList();
  }

  BibleLocation? getLastLocationForBook(int bookId) {
    try {
      return _readingHistory.firstWhere(
        (location) => location.bookId == bookId,
      );
    } catch (e) {
      return null;
    }
  }

  // Reading session tracking
  Future<void> updateScrollPosition(int bookId, int chapter, double scrollPosition) async {
    if (_currentLocation?.bookId == bookId && _currentLocation?.chapter == chapter) {
      final updatedLocation = BibleLocation(
        bookId: bookId,
        bookName: _currentLocation!.bookName,
        chapter: chapter,
        scrollPosition: scrollPosition,
        timestamp: DateTime.now(),
      );

      _currentLocation = updatedLocation;
      await _persistCurrentLocation();
      notifyListeners();
    }
  }

  // Reading statistics
  Map<String, dynamic> getReadingStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisWeek = today.subtract(Duration(days: today.weekday - 1));
    final thisMonth = DateTime(now.year, now.month, 1);

    final todayReading = _readingHistory.where((l) =>
      DateTime(l.timestamp.year, l.timestamp.month, l.timestamp.day) == today
    ).toList();

    final weekReading = _readingHistory.where((l) =>
      l.timestamp.isAfter(thisWeek)
    ).toList();

    final monthReading = _readingHistory.where((l) =>
      l.timestamp.isAfter(thisMonth)
    ).toList();

    final uniqueBooksThisMonth = monthReading
        .map((l) => l.bookId)
        .toSet()
        .length;

    return {
      'chaptersToday': todayReading.length,
      'chaptersThisWeek': weekReading.length,
      'chaptersThisMonth': monthReading.length,
      'uniqueBooksThisMonth': uniqueBooksThisMonth,
      'totalChaptersRead': _readingHistory.length,
      'currentStreak': _calculateReadingStreak(),
    };
  }

  int _calculateReadingStreak() {
    if (_readingHistory.isEmpty) return 0;

    final now = DateTime.now();
    var currentDate = DateTime(now.year, now.month, now.day);
    var streak = 0;

    // Check if user read today
    final todayReading = _readingHistory.any((l) =>
      DateTime(l.timestamp.year, l.timestamp.month, l.timestamp.day) == currentDate
    );

    if (!todayReading) {
      // If no reading today, check yesterday
      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    // Count consecutive days with reading
    while (true) {
      final dayReading = _readingHistory.any((l) =>
        DateTime(l.timestamp.year, l.timestamp.month, l.timestamp.day) == currentDate
      );

      if (dayReading) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }
    }

    return streak;
  }

  // Bookmarks functionality
  Future<void> addBookmark(BibleLocation location, {String? note}) async {
    final prefs = await SharedPreferences.getInstance();
    final bookmarksData = prefs.getString('bible_bookmarks') ?? '[]';
    final bookmarks = jsonDecode(bookmarksData) as List;

    final bookmark = {
      ...location.toJson(),
      'note': note,
      'createdAt': DateTime.now().toIso8601String(),
    };

    bookmarks.add(bookmark);
    await prefs.setString('bible_bookmarks', jsonEncode(bookmarks));
    notifyListeners();
  }

  Future<List<Map<String, dynamic>>> getBookmarks() async {
    final prefs = await SharedPreferences.getInstance();
    final bookmarksData = prefs.getString('bible_bookmarks') ?? '[]';
    return List<Map<String, dynamic>>.from(jsonDecode(bookmarksData));
  }

  Future<void> removeBookmark(BibleLocation location) async {
    final prefs = await SharedPreferences.getInstance();
    final bookmarksData = prefs.getString('bible_bookmarks') ?? '[]';
    final bookmarks = jsonDecode(bookmarksData) as List;

    bookmarks.removeWhere((bookmark) =>
      bookmark['bookId'] == location.bookId &&
      bookmark['chapter'] == location.chapter
    );

    await prefs.setString('bible_bookmarks', jsonEncode(bookmarks));
    notifyListeners();
  }
}
