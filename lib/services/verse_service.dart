import 'package:echoverse/models/verse.dart';
import 'package:echoverse/utils/storage_helper.dart';
import 'package:echoverse/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class VerseService {
  List<Verse> _verses = [];
  final FirestoreService _firestoreService = FirestoreService();
  final Set<String> _favoriteIds = {};
  String? _currentUserId;

  Future<void> initialize() async {
    final savedVerses = await StorageHelper.loadVerses();
    if (savedVerses.isEmpty) {
      _verses = _getSampleVerses();
      await StorageHelper.saveVerses(_verses.map((v) => v.toJson()).toList());
    } else {
      _verses = savedVerses.map((json) => Verse.fromJson(json)).toList();
    }
  }

  /// Set the current user and load their favorites
  Future<void> setCurrentUser(String? userId) async {
    _currentUserId = userId;
    if (userId != null) {
      await _loadFavorites(userId);
    } else {
      _favoriteIds.clear();
    }
  }

  /// Load favorites from Firestore
  Future<void> _loadFavorites(String userId) async {
    try {
      final favorites = await _firestoreService.getFavorites(userId);
      _favoriteIds.clear();
      _favoriteIds.addAll(favorites.map((f) => f['verseId'] as String));
    } catch (e) {
      // Silently fail if offline or error
    }
  }

  /// Add verse to favorites
  Future<void> addToFavorites(Verse verse) async {
    if (_currentUserId == null) return;
    
    try {
      await _firestoreService.addToFavorites(_currentUserId!, verse);
      _favoriteIds.add(verse.id);
    } catch (e) {
      throw Exception('Failed to add to favorites: $e');
    }
  }

  /// Remove verse from favorites
  Future<void> removeFromFavorites(String verseId) async {
    if (_currentUserId == null) return;
    
    try {
      await _firestoreService.removeFromFavorites(_currentUserId!, verseId);
      _favoriteIds.remove(verseId);
    } catch (e) {
      throw Exception('Failed to remove from favorites: $e');
    }
  }

  /// Check if verse is favorited
  bool isFavorite(String verseId) {
    return _favoriteIds.contains(verseId);
  }

  /// Get all favorite verses
  Future<List<Verse>> getFavoriteVerses() async {
    if (_currentUserId == null) return [];
    
    try {
      // Get favorites from Firestore
      final favorites = await _firestoreService.getFavorites(_currentUserId!);
      
      // Convert Firestore favorites to Verse objects
      return favorites.map((f) {
        return Verse(
          id: f['verseId'] as String,
          book: f['book'] as String? ?? 'Unknown',
          chapter: f['chapter'] as int? ?? 0,
          verseNumber: f['verseNumber'] as int? ?? 0,
          text: f['text'] as String? ?? '',
          translation: f['translation'] as String? ?? 'BSB',
          createdAt: f['addedAt'] != null 
              ? (f['addedAt'] as Timestamp).toDate()
              : DateTime.now(),
          updatedAt: f['addedAt'] != null 
              ? (f['addedAt'] as Timestamp).toDate()
              : DateTime.now(),
        );
      }).toList();
    } catch (e) {
      // Fallback to local favorites if Firestore fails
      return _verses.where((v) => _favoriteIds.contains(v.id)).toList();
    }
  }

  /// Stream of favorites (real-time updates)
  Stream<List<String>> favoritesStream() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }
    return _firestoreService.favoritesStream(_currentUserId!).map(
      (favorites) => favorites.map((f) => f['verseId'] as String).toList(),
    );
  }

  List<Verse> getAllVerses() => _verses;

  /// Get recent verses (sorted by creation date)
  Future<List<Verse>> getRecentVerses({int limit = 5}) async {
    final sorted = List<Verse>.from(_verses)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sorted.take(limit).toList();
  }

  Verse? getVerseById(String id) {
    try {
      return _verses.firstWhere((v) => v.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Verse> _getSampleVerses() {
    final now = DateTime.now();
    return [
      Verse(
        id: 'v1',
        book: 'John',
        chapter: 3,
        verseNumber: 16,
        text: 'For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.',
        translation: 'NIV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v2',
        book: 'Philippians',
        chapter: 4,
        verseNumber: 13,
        text: 'I can do all things through Christ who strengthens me.',
        translation: 'NKJV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v3',
        book: 'Psalm',
        chapter: 23,
        verseNumber: 1,
        text: 'The Lord is my shepherd; I shall not want.',
        translation: 'KJV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v4',
        book: 'Proverbs',
        chapter: 3,
        verseNumber: 5,
        text: 'Trust in the Lord with all your heart and lean not on your own understanding.',
        translation: 'NIV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v5',
        book: 'Romans',
        chapter: 8,
        verseNumber: 28,
        text: 'And we know that in all things God works for the good of those who love him, who have been called according to his purpose.',
        translation: 'NIV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v6',
        book: 'Jeremiah',
        chapter: 29,
        verseNumber: 11,
        text: 'For I know the plans I have for you, declares the Lord, plans to prosper you and not to harm you, plans to give you hope and a future.',
        translation: 'NIV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v7',
        book: 'Matthew',
        chapter: 11,
        verseNumber: 28,
        text: 'Come to me, all you who are weary and burdened, and I will give you rest.',
        translation: 'NIV',
        createdAt: now,
        updatedAt: now,
      ),
      Verse(
        id: 'v8',
        book: 'Isaiah',
        chapter: 40,
        verseNumber: 31,
        text: 'But those who hope in the Lord will renew their strength. They will soar on wings like eagles; they will run and not grow weary, they will walk and not be faint.',
        translation: 'NIV',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}
