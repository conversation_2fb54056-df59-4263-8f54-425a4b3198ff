import 'package:flutter/material.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/verse_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/models/verse.dart';
import 'package:echoverse/widgets/verse_card.dart';
import 'package:echoverse/widgets/common/loading_state.dart';
import 'package:echoverse/widgets/common/empty_state.dart';
import 'package:echoverse/design_system/app_spacing.dart';
import 'package:echoverse/screens/karaoke_screen.dart';
import 'package:echoverse/services/settings_service.dart';

enum SortOption { newest, mostPracticed, difficulty }

class SavedTab extends StatefulWidget {
  final ProgressService progressService;
  final VerseService verseService;
  final UserService userService;

  const SavedTab({
    super.key,
    required this.progressService,
    required this.verseService,
    required this.userService,
  });

  @override
  State<SavedTab> createState() => _SavedTabState();
}

class _SavedTabState extends State<SavedTab> {
  List<Verse> _allVerses = [];
  List<Verse> _masteredVerses = [];
  List<Verse> _favoriteVerses = [];
  bool _isLoading = true;
  int _selectedSection = 0; // 0 = My Verses, 1 = Memorized, 2 = Favorites
  String _searchQuery = '';
  SortOption _sortOption = SortOption.newest;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    await widget.verseService.initialize();
    final currentUser = await widget.userService.getCurrentUser();
    if (currentUser == null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }
    
    // Set current user for VerseService to load favorites
    await widget.verseService.setCurrentUser(currentUser.id);
    
    final allVerses = widget.verseService.getAllVerses();
    final masteredProgress = widget.progressService.getMasteredVerses(currentUser.id);
    final favoriteVerses = await widget.verseService.getFavoriteVerses();
    
    setState(() {
      _allVerses = allVerses;
      _masteredVerses = masteredProgress
          .map((p) => allVerses.firstWhere(
                (v) => v.id == p.verseId,
                orElse: () => allVerses.first,
              ))
          .toList();
      _favoriteVerses = favoriteVerses;
      _isLoading = false;
    });
    
    _applySorting();
  }

  void _applySorting() {
    List<Verse> versesToSort = [];
    switch (_selectedSection) {
      case 0:
        versesToSort = List.from(_allVerses);
        break;
      case 1:
        versesToSort = List.from(_masteredVerses);
        break;
      case 2:
        versesToSort = List.from(_favoriteVerses);
        break;
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      versesToSort = versesToSort
          .where((verse) =>
              verse.text.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              verse.reference.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    // Apply sorting
    switch (_sortOption) {
      case SortOption.newest:
        versesToSort.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.mostPracticed:
        widget.userService.getCurrentUser().then((user) {
          if (user == null) return;
          versesToSort.sort((a, b) {
            final progressA = widget.progressService.getProgressForVerse(user.id, a.id);
            final progressB = widget.progressService.getProgressForVerse(user.id, b.id);
            return (progressB?.practiceCount ?? 0).compareTo(progressA?.practiceCount ?? 0);
          });
          if (mounted) setState(() {});
        });
        break;
      case SortOption.difficulty:
        // Sort by text length as proxy for difficulty
        versesToSort.sort((a, b) => b.text.length.compareTo(a.text.length));
        break;
    }

    setState(() {
      switch (_selectedSection) {
        case 0:
          _allVerses = versesToSort;
          break;
        case 1:
          _masteredVerses = versesToSort;
          break;
        case 2:
          _favoriteVerses = versesToSort;
          break;
      }
    });
  }

  void _navigateToKaraoke(Verse verse) {
    widget.userService.getCurrentUser().then((user) {
      if (user == null || !mounted) return;
      final settingsService = SettingsService();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => KaraokeScreen(
            verse: verse,
            userId: user.id,
            progressService: widget.progressService,
            settingsService: settingsService,
          ),
        ),
      ).then((_) => _initialize());
    });
  }

  Future<void> _toggleFavorite(Verse verse) async {
    try {
      final isFavorite = widget.verseService.isFavorite(verse.id);

      if (isFavorite) {
        await widget.verseService.removeFromFavorites(verse.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${verse.reference} removed from favorites'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        await widget.verseService.addToFavorites(verse);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${verse.reference} added to favorites'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }

      // Refresh the data
      _initialize();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update favorites: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.colorScheme.surface,
        body: const LoadingState(message: 'Loading your saved verses...'),
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(AppSpacing.screenPadding, AppSpacing.lg, AppSpacing.screenPadding, AppSpacing.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Saved',
                    style: theme.textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    'Organize your verses and track progress',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.65),
                    ),
                  ),
                ],
              ),
            ),

            // Search Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: TextField(
                onChanged: (value) {
                  setState(() => _searchQuery = value);
                  _applySorting();
                },
                decoration: InputDecoration(
                  hintText: 'Search verses...',
                  prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: theme.colorScheme.onSurface),
                          onPressed: () {
                            setState(() => _searchQuery = '');
                            _applySorting();
                          },
                        )
                      : null,
                  filled: true,
                  fillColor: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Section Tabs
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                children: [
                  Expanded(
                    child: _buildSectionTab(context, 'My Verses', _allVerses.length, 0),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSectionTab(context, 'Memorized', _masteredVerses.length, 1),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSectionTab(context, 'Favorites', _favoriteVerses.length, 2),
                  ),
                ],
              ),
            ),

            // Sort Option
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding, vertical: AppSpacing.sm),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sort: ',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: SortOption.values.map((option) {
                      final isSelected = _sortOption == option;
                      return ChoiceChip(
                        label: Text(_getSortLabel(option)),
                        selected: isSelected,
                        onSelected: (_) {
                          setState(() => _sortOption = option);
                          _applySorting();
                        },
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 8),

            // Content
            Expanded(
              child: _buildContent(theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTab(BuildContext context, String label, int count, int index) {
    final theme = Theme.of(context);
    final isSelected = _selectedSection == index;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() => _selectedSection = index);
          _applySorting();
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                '$count',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected
                      ? Colors.white.withValues(alpha: 0.9)
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getSortLabel(SortOption option) {
    switch (option) {
      case SortOption.newest:
        return 'Newest';
      case SortOption.mostPracticed:
        return 'Most Practiced';
      case SortOption.difficulty:
        return 'Difficulty';
    }
  }

  Widget _buildContent(ThemeData theme) {
    switch (_selectedSection) {
      case 0:
        return _buildMyVersesList(theme);
      case 1:
        return _buildMemorizedList(theme);
      case 2:
        return _buildPlaylistsList(theme);
      default:
        return _buildMyVersesList(theme);
    }
  }

  Widget _buildMyVersesList(ThemeData theme) {
    final verses = _searchQuery.isNotEmpty
        ? _allVerses.where((v) =>
            v.text.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            v.reference.toLowerCase().contains(_searchQuery.toLowerCase()))
        .toList()
        : _allVerses;

    if (verses.isEmpty) {
      return _searchQuery.isNotEmpty
          ? EmptyState.noSearchResults(query: _searchQuery)
          : EmptyState.noVerses();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: verses.length,
      itemBuilder: (context, index) {
        final verse = verses[index];
        return FutureBuilder(
          future: widget.userService.getCurrentUser(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) return const SizedBox();
            final progress = widget.progressService.getProgressForVerse(
              snapshot.data!.id,
              verse.id,
            );
            return Dismissible(
              key: Key('verse_${verse.id}'),
              direction: DismissDirection.endToStart,
              background: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(right: 20),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.delete, color: Colors.white, size: 24),
                    SizedBox(height: 4),
                    Text('Remove', style: TextStyle(color: Colors.white, fontSize: 12)),
                  ],
                ),
              ),
              confirmDismiss: (direction) async {
                return await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Remove Verse'),
                    content: Text('Remove ${verse.reference} from your saved verses?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Remove'),
                      ),
                    ],
                  ),
                ) ?? false;
              },
              onDismissed: (direction) async {
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                try {
                  // Remove from favorites if it's in favorites
                  if (widget.verseService.isFavorite(verse.id)) {
                    await widget.verseService.removeFromFavorites(verse.id);
                  }

                  // Show success message
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('${verse.reference} removed from saved verses'),
                        action: SnackBarAction(
                          label: 'Undo',
                          onPressed: () async {
                            // Re-add to favorites
                            await widget.verseService.addToFavorites(verse);
                            _initialize(); // Refresh the list
                          },
                        ),
                      ),
                    );
                    _initialize(); // Refresh the list
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(content: Text('Failed to remove verse: $e')),
                    );
                  }
                }
              },
              child: VerseCard(
                verse: verse,
                progress: progress,
                onTap: () => _navigateToKaraoke(verse),
                onFavorite: () => _toggleFavorite(verse),
                isFavorite: widget.verseService.isFavorite(verse.id),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildMemorizedList(ThemeData theme) {
    if (_masteredVerses.isEmpty) {
      return EmptyState.noPracticeHistory();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: _masteredVerses.length,
      itemBuilder: (context, index) {
        final verse = _masteredVerses[index];
        return FutureBuilder(
          future: widget.userService.getCurrentUser(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) return const SizedBox();
            final progress = widget.progressService.getProgressForVerse(
              snapshot.data!.id,
              verse.id,
            );
            return VerseCard(
              verse: verse,
              progress: progress,
              onTap: () => _navigateToKaraoke(verse),
              onFavorite: () => _toggleFavorite(verse),
              isFavorite: widget.verseService.isFavorite(verse.id),
            );
          },
        );
      },
    );
  }

  Widget _buildPlaylistsList(ThemeData theme) {
    if (_favoriteVerses.isEmpty) {
      return EmptyState.noFavorites();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: _favoriteVerses.length,
      itemBuilder: (context, index) {
        final verse = _favoriteVerses[index];
        return FutureBuilder(
          future: widget.userService.getCurrentUser(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) return const SizedBox();
            final progress = widget.progressService.getProgressForVerse(
              snapshot.data!.id,
              verse.id,
            );
            return Dismissible(
              key: Key('favorite_${verse.id}'),
              direction: DismissDirection.endToStart,
              background: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(right: 20),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.favorite_border, color: Colors.white, size: 24),
                    SizedBox(height: 4),
                    Text('Unfavorite', style: TextStyle(color: Colors.white, fontSize: 12)),
                  ],
                ),
              ),
              confirmDismiss: (direction) async {
                return await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Remove from Favorites'),
                    content: Text('Remove ${verse.reference} from your favorites?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Remove'),
                      ),
                    ],
                  ),
                ) ?? false;
              },
              onDismissed: (direction) async {
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                try {
                  await widget.verseService.removeFromFavorites(verse.id);

                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('${verse.reference} removed from favorites'),
                        action: SnackBarAction(
                          label: 'Undo',
                          onPressed: () async {
                            await widget.verseService.addToFavorites(verse);
                            _initialize();
                          },
                        ),
                      ),
                    );
                    _initialize();
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(content: Text('Failed to remove from favorites: $e')),
                    );
                  }
                }
              },
              child: VerseCard(
                verse: verse,
                progress: progress,
                onTap: () => _navigateToKaraoke(verse),
                onFavorite: () => _toggleFavorite(verse),
                isFavorite: widget.verseService.isFavorite(verse.id),
              ),
            );
          },
        );
      },
    );
  }


}
