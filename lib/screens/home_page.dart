import 'package:flutter/material.dart';
import 'package:echoverse/models/verse.dart';
import 'package:echoverse/models/user.dart';
import 'package:echoverse/services/verse_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/widgets/verse_card.dart';
import 'package:echoverse/screens/karaoke_screen.dart';
import 'package:echoverse/screens/progress_screen.dart';
import 'package:echoverse/screens/settings_screen.dart';
import 'package:echoverse/screens/bible_books_screen.dart';

class HomePage extends StatefulWidget {
  final SettingsService settingsService;

  const HomePage({super.key, required this.settingsService});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final VerseService _verseService = VerseService();
  final UserService _userService = UserService();
  final ProgressService _progressService = ProgressService();
  
  List<Verse> _verses = [];
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    await _verseService.initialize();
    await _progressService.initialize();
    _currentUser = await _userService.getCurrentUser();
    setState(() {
      _verses = _verseService.getAllVerses();
      _isLoading = false;
    });
  }

  void _navigateToKaraoke(Verse verse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(verse: verse, userId: _currentUser!.id, progressService: _progressService, settingsService: widget.settingsService),
      ),
    ).then((_) => setState(() {}));
  }

  void _navigateToProgress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProgressScreen(userId: _currentUser!.id, progressService: _progressService, verseService: _verseService),
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SettingsScreen(settingsService: widget.settingsService),
      ),
    );
  }

  void _navigateToBible() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BibleBooksScreen(
          progressService: _progressService,
          userService: _userService,
        ),
      ),
    );
  }

  Widget _buildIconButton({required BuildContext context, required IconData icon, required VoidCallback onTap}) {
    final theme = Theme.of(context);
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(14),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(14),
            border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.1), width: 1),
          ),
          child: Icon(icon, color: theme.colorScheme.primary, size: 22),
        ),
      ),
    );
  }

  Widget _buildStatsCard(String title, String value, IconData icon, Color color, BuildContext context) {
    final theme = Theme.of(context);
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20, color: color),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.colorScheme.surface,
        body: Center(child: CircularProgressIndicator(color: theme.colorScheme.primary)),
      );
    }

    final masteredCount = _progressService.getMasteredVerses(_currentUser!.id).length;
    final totalPractice = _progressService.getTotalPracticeCount(_currentUser!.id);
    final totalVerses = _verses.length;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'VerseFlow',
                          style: theme.textTheme.headlineLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            letterSpacing: -0.5,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          'Master Scripture through practice',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.65),
                            letterSpacing: 0.2,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      _buildIconButton(
                        context: context,
                        icon: Icons.menu_book,
                        onTap: _navigateToBible,
                      ),
                      const SizedBox(width: 10),
                      _buildIconButton(
                        context: context,
                        icon: Icons.bar_chart_rounded,
                        onTap: _navigateToProgress,
                      ),
                      const SizedBox(width: 10),
                      _buildIconButton(
                        context: context,
                        icon: Icons.settings_outlined,
                        onTap: _navigateToSettings,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Stats Dashboard
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  _buildStatsCard(
                    'Mastered',
                    '$masteredCount',
                    Icons.check_circle,
                    theme.colorScheme.secondary,
                    context,
                  ),
                  const SizedBox(width: 12),
                  _buildStatsCard(
                    'Practiced',
                    '$totalPractice',
                    Icons.repeat,
                    theme.colorScheme.primary,
                    context,
                  ),
                  const SizedBox(width: 12),
                  _buildStatsCard(
                    'Total',
                    '$totalVerses',
                    Icons.menu_book,
                    theme.colorScheme.tertiary,
                    context,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Quick Actions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  Expanded(
                    child: _buildQuickAction(
                      context: context,
                      icon: Icons.menu_book_outlined,
                      label: 'Browse Bible',
                      color: theme.colorScheme.primary,
                      onTap: _navigateToBible,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildQuickAction(
                      context: context,
                      icon: Icons.analytics_outlined,
                      label: 'View Progress',
                      color: theme.colorScheme.secondary,
                      onTap: _navigateToProgress,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Verses List Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'My Verses',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_verses.isEmpty)
                    Text(
                      'Add verses from Bible',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Verses List
            Expanded(
              child: _verses.isEmpty
                  ? _buildEmptyState(context)
                  : ListView.builder(
                      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                      itemCount: _verses.length,
                      itemBuilder: (context, index) {
                        final verse = _verses[index];
                        final progress = _progressService.getProgressForVerse(_currentUser!.id, verse.id);
                        return VerseCard(verse: verse, progress: progress, onTap: () => _navigateToKaraoke(verse));
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  label,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ),
              Icon(Icons.chevron_right, color: color.withValues(alpha: 0.6), size: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book_outlined,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No verses yet',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Browse the Bible to add verses\nand start memorizing',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToBible,
              icon: const Icon(Icons.menu_book),
              label: const Text('Browse Bible'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
