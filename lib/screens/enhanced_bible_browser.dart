import 'package:flutter/material.dart';
import 'package:echoverse/models/bible_book.dart';
import 'package:echoverse/models/bible_verse.dart';
import 'package:echoverse/services/bible_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/verse_service.dart';
import 'package:echoverse/services/audio_service.dart';
import 'package:echoverse/screens/karaoke_screen.dart';

class EnhancedBibleBrowser extends StatefulWidget {
  const EnhancedBibleBrowser({super.key});

  @override
  State<EnhancedBibleBrowser> createState() => _EnhancedBibleBrowserState();
}

class _EnhancedBibleBrowserState extends State<EnhancedBibleBrowser> {
  final BibleService _bibleService = BibleService();
  final VerseService _verseService = VerseService();
  final ProgressService _progressService = ProgressService();
  final UserService _userService = UserService();
  final SettingsService _settingsService = SettingsService();
  final AudioService _audioService = AudioService();

  List<BibleBook> _books = [];
  BibleBook? _selectedBook;
  int? _selectedChapter;
  List<BibleVerse> _verses = [];
  String _searchQuery = '';
  List<BibleVerse> _searchResults = [];

  bool _isLoading = false;
  bool _isSearching = false;
  bool _isPlayingChapter = false;
  bool _isSelectionMode = false;
  Set<String> _selectedVerseIds = {};

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    await _settingsService.initialize();
    await _progressService.initialize();
    await _verseService.initialize();
    
    // Set Bible translation from settings
    await _bibleService.setTranslation(_settingsService.bibleTranslation.code);
    
    // Set current user for Firestore sync
    final currentUser = await _userService.getCurrentUser();
    if (currentUser != null) {
      await _verseService.setCurrentUser(currentUser.id);
      await _progressService.setCurrentUser(currentUser.id);
    }
    
    // Listen to settings changes to update translation
    _settingsService.addListener(_onSettingsChanged);
    
    await _loadBooks();
  }
  
  void _onSettingsChanged() {
    // Reload when translation changes
    _bibleService.setTranslation(_settingsService.bibleTranslation.code);
    if (_selectedBook != null && _selectedChapter != null) {
      _selectChapter(_selectedChapter!);
    }
  }
  
  @override
  void dispose() {
    _settingsService.removeListener(_onSettingsChanged);
    super.dispose();
  }

  Future<void> _loadBooks() async {
    setState(() => _isLoading = true);
    final oldTestament = await _bibleService.getOldTestamentBooks();
    final newTestament = await _bibleService.getNewTestamentBooks();
    setState(() {
      _books = [...oldTestament, ...newTestament];
      _isLoading = false;
    });
  }

  Future<void> _selectBook(BibleBook book) async {
    setState(() {
      _selectedBook = book;
      _selectedChapter = null;
      _verses = [];
    });
  }

  Future<void> _selectChapter(int chapter) async {
    setState(() => _isLoading = true);
    final verses = await _bibleService.getChapter(_selectedBook!.id, chapter);
    setState(() {
      _selectedChapter = chapter;
      _verses = verses;
      _isLoading = false;
      // Clear selections when changing chapters
      _selectedVerseIds.clear();
      _isSelectionMode = false;
    });
  }

  Future<void> _search(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchQuery = '';
        _isSearching = false;
        _searchResults = [];
        _selectedVerseIds.clear();
        _isSelectionMode = false;
      });
      return;
    }

    setState(() {
      _searchQuery = query;
      _isSearching = true;
      _selectedVerseIds.clear();
      _isSelectionMode = false;
    });

    final results = await _bibleService.searchVerses(query);
    setState(() {
      _searchResults = results;
    });
  }

  Future<void> _listenToChapter() async {
    if (_verses.isEmpty) return;
    
    setState(() => _isPlayingChapter = true);
    
    final chapterText = _verses.map((v) => v.text).join(' ');
    await _audioService.speakVerse(
      chapterText,
      onWord: (_) {},
      onPlayingStateChanged: (playing) {
        if (mounted) {
          setState(() => _isPlayingChapter = playing);
        }
      },
      onPositionChanged: (_) {},
      onDurationChanged: (_) {},
    );
  }

  Future<void> _startKaraoke(BibleVerse verse) async {
    final currentUser = await _userService.getCurrentUser();
    if (currentUser == null || !mounted) return;

    final verseModel = verse.toVerse(_selectedBook?.name ?? 'Bible');
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: verseModel,
          userId: currentUser.id,
          progressService: _progressService,
          settingsService: _settingsService,
        ),
      ),
    );
  }


  String _getVerseId(BibleVerse verse) {
    // Match the format used in BibleVerse.toVerse() method
    return 'bible_${verse.bookId}_${verse.chapter}_${verse.verse}';
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedVerseIds.clear();
      }
    });
  }

  void _toggleVerseSelection(BibleVerse verse) {
    setState(() {
      final verseId = _getVerseId(verse);
      if (_selectedVerseIds.contains(verseId)) {
        _selectedVerseIds.remove(verseId);
      } else {
        _selectedVerseIds.add(verseId);
      }
      // Exit selection mode if no verses selected
      if (_selectedVerseIds.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  void _selectAllVerses() {
    setState(() {
      _selectedVerseIds.clear();
      final sourceList = _isSearching ? _searchResults : _verses;
      for (final verse in sourceList) {
        _selectedVerseIds.add(_getVerseId(verse));
      }
      _isSelectionMode = true;
    });
  }

  void _deselectAllVerses() {
    setState(() {
      _selectedVerseIds.clear();
      _isSelectionMode = false;
    });
  }

  List<BibleVerse> _getSelectedVerses() {
    final sourceList = _isSearching ? _searchResults : _verses;
    return sourceList.where((verse) => _selectedVerseIds.contains(_getVerseId(verse))).toList();
  }

  Future<void> _addSelectedToFavorites() async {
    final selectedVerses = _getSelectedVerses();
    if (selectedVerses.isEmpty) return;

    final currentUser = await _userService.getCurrentUser();
    if (currentUser == null) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please sign in to add favorites'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final bookName = _selectedBook?.name ?? 'Bible';
    int successCount = 0;
    int failCount = 0;

    for (final verse in selectedVerses) {
      try {
        // For search results, we need to get the book name from the verse
        final verseBookName = _isSearching 
            ? (await _bibleService.getBook(verse.bookId))?.name ?? bookName
            : bookName;
        final verseToSave = verse.toVerse(verseBookName);
        await _verseService.addToFavorites(verseToSave);
        successCount++;
      } catch (e) {
        failCount++;
      }
    }

    if (!mounted) return;
    
    setState(() {
      _selectedVerseIds.clear();
      _isSelectionMode = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          failCount > 0
              ? 'Added $successCount verse(s) to favorites. $failCount failed.'
              : 'Added $successCount verse(s) to favorites',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _practiceSelectedVerses() async {
    final selectedVerses = _getSelectedVerses();
    if (selectedVerses.isEmpty) return;

    final currentUser = await _userService.getCurrentUser();
    if (currentUser == null || !mounted) return;

    // Sort verses by book, chapter, then verse number
    selectedVerses.sort((a, b) {
      if (a.bookId != b.bookId) return a.bookId.compareTo(b.bookId);
      if (a.chapter != b.chapter) return a.chapter.compareTo(b.chapter);
      return a.verse.compareTo(b.verse);
    });
    
    final combinedText = selectedVerses.map((v) => v.text).join(' ');
    final firstVerse = selectedVerses.first;
    final bookName = _selectedBook?.name ?? 'Bible';
    final verse = firstVerse.toVerse(bookName).copyWith(
      text: combinedText,
      verseNumber: selectedVerses.length == 1 ? firstVerse.verse : 0,
      id: 'bible_selected_${_selectedVerseIds.join('_')}',
    );

    setState(() {
      _selectedVerseIds.clear();
      _isSelectionMode = false;
    });

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: verse,
          userId: currentUser.id,
          progressService: _progressService,
          settingsService: _settingsService,
        ),
      ),
    );
  }

  Future<void> _toggleFavorite(BibleVerse verse) async {
    final currentUser = await _userService.getCurrentUser();
    if (currentUser == null) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please sign in to manage favorites'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final verseId = _getVerseId(verse);
    final isFavorite = _verseService.isFavorite(verseId);
    final bookName = _selectedBook?.name ?? 'Unknown';
    
    try {
      if (isFavorite) {
        // Remove from favorites
        await _verseService.removeFromFavorites(verseId);
        
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Removed $bookName ${verse.chapter}:${verse.verse} from favorites'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // Add to favorites
        final verseToSave = verse.toVerse(bookName);
        await _verseService.addToFavorites(verseToSave);
        
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added $bookName ${verse.chapter}:${verse.verse} to favorites'),
            duration: const Duration(seconds: 2),
            action: SnackBarAction(
              label: 'View',
              onPressed: () {
                // Navigate to saved tab
              },
            ),
          ),
        );
      }
      
      // Update UI state
      if (mounted) {
        setState(() {
          // State will be updated by the isFavorite check in the build method
        });
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to ${isFavorite ? 'remove' : 'add'} favorite: $e'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header with Translation Selector and Search
            _buildHeader(theme),
            
            // Main Content: Three-column or search results
            Expanded(
              child: _isSearching && _searchQuery.isNotEmpty
                  ? _buildSearchResults(theme)
                  : _buildThreeColumnView(theme),
            ),
            
            // Bottom Actions Bar
            if (_selectedBook != null && _selectedChapter != null)
              _isSelectionMode && _selectedVerseIds.isNotEmpty
                  ? _buildSelectionActions(theme)
                  : _buildBottomActions(theme),
            // Selection actions for search results
            if (_isSearching && _isSelectionMode && _selectedVerseIds.isNotEmpty)
              _buildSelectionActions(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
          ),
        ),
      ),
      child: Column(
        children: [
          // Translation Display and Dark Mode Toggle
          Row(
            children: [
              // Translation Badge (tap to change in settings)
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    // Navigate to settings
                    Navigator.pushNamed(context, '/settings');
                  },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.colorScheme.primary.withValues(alpha: 0.2),
                    ),
                  ),
                    child: Row(
                      children: [
                        Icon(Icons.menu_book, color: theme.colorScheme.primary, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _settingsService.bibleTranslation.code,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              Text(
                                _settingsService.bibleTranslation.displayName,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        Icon(Icons.arrow_forward_ios, color: theme.colorScheme.primary, size: 16),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Selection Mode Toggle (show when chapter is selected or when searching)
              if (_selectedChapter != null || _isSearching)
                IconButton(
                  icon: Icon(
                    _isSelectionMode ? Icons.check_circle : Icons.check_circle_outline,
                    color: _isSelectionMode ? theme.colorScheme.primary : null,
                  ),
                  onPressed: _toggleSelectionMode,
                  tooltip: _isSelectionMode ? 'Exit selection mode' : 'Select verses',
                ),
              // Dark Mode Toggle
              IconButton(
                icon: Icon(
                  theme.brightness == Brightness.dark
                      ? Icons.light_mode
                      : Icons.dark_mode,
                ),
                onPressed: () {
                  _settingsService.setThemeMode(
                    theme.brightness == Brightness.dark
                        ? ThemeMode.light
                        : ThemeMode.dark,
                  );
                },
                tooltip: 'Toggle theme',
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Search Bar
          TextField(
            onChanged: _search,
            decoration: InputDecoration(
              hintText: 'Search verses or reference (e.g., John 3:16)',
              prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear, color: theme.colorScheme.onSurface),
                      onPressed: () => _search(''),
                    )
                  : null,
              filled: true,
              fillColor: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThreeColumnView(ThemeData theme) {
    return Row(
      children: [
        // Books Column
        Container(
          width: 120,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            border: Border(
              right: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
              ),
            ),
          ),
          child: _buildBooksColumn(theme),
        ),
        // Chapters Column (if book selected)
        if (_selectedBook != null)
          Container(
            width: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
              border: Border(
                right: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
            ),
            child: _buildChaptersColumn(theme),
          ),
        // Verses Column (if chapter selected)
        if (_selectedChapter != null)
          Expanded(
            child: _buildVersesColumn(theme),
          ),
      ],
    );
  }

  Widget _buildBooksColumn(ThemeData theme) {
    if (_isLoading && _books.isEmpty) {
      return Center(
        child: CircularProgressIndicator(color: theme.colorScheme.primary),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _books.length,
      itemBuilder: (context, index) {
        final book = _books[index];
        final isSelected = _selectedBook?.id == book.id;

        return InkWell(
          onTap: () => _selectBook(book),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            color: isSelected
                ? theme.colorScheme.primary.withValues(alpha: 0.15)
                : Colors.transparent,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  book.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChaptersColumn(ThemeData theme) {
    return FutureBuilder<int>(
      future: _bibleService.getChapterCount(_selectedBook!.id),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Center(
            child: CircularProgressIndicator(color: theme.colorScheme.primary),
          );
        }

        final chapterCount = snapshot.data!;

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: chapterCount,
          itemBuilder: (context, index) {
            final chapter = index + 1;
            final isSelected = _selectedChapter == chapter;

            return InkWell(
              onTap: () => _selectChapter(chapter),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                color: isSelected
                    ? theme.colorScheme.primary.withValues(alpha: 0.15)
                    : Colors.transparent,
                child: Center(
                  child: Text(
                    '$chapter',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildVersesColumn(ThemeData theme) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(color: theme.colorScheme.primary),
      );
    }

    final verseCount = _verses.length;
    final estimatedReadingTime = (verseCount * 10 / 60).ceil(); // ~10 seconds per verse

    return Column(
      children: [
        // Modern Chapter Info Card
        _buildChapterInfoCard(theme, verseCount, estimatedReadingTime),
        
        // Reading Progress Bar
        _buildReadingProgressBar(theme),
        
        // Font Size Control
        _buildFontSizeControl(theme),
        
        // Verses List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            itemCount: _verses.length,
            itemBuilder: (context, index) {
              final verse = _verses[index];
              final isFirstVerse = index == 0;
              final isLastVerse = index == _verses.length - 1;
              return _buildModernVerseCard(verse, theme, index, isFirstVerse, isLastVerse);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildChapterInfoCard(ThemeData theme, int verseCount, int readingTime) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            theme.colorScheme.secondaryContainer.withValues(alpha: 0.2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Chapter $_selectedChapter',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Icon(Icons.book_outlined, 
                color: theme.colorScheme.primary,
                size: 24,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _selectedBook?.name ?? '',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildInfoChip(theme, Icons.format_list_numbered, '$verseCount verses'),
              const SizedBox(width: 8),
              _buildInfoChip(theme, Icons.schedule, '~$readingTime min read'),
              const SizedBox(width: 8),
              _buildInfoChip(theme, Icons.translate, _settingsService.bibleTranslation.code),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(ThemeData theme, IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.onSurface.withValues(alpha: 0.7)),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingProgressBar(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.menu_book, 
            size: 16, 
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: _verses.isEmpty ? 0 : 1.0,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation(theme.colorScheme.primary),
                minHeight: 6,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${_verses.length} verses',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  double _readerFontSize = 1.0;

  Widget _buildFontSizeControl(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 12, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.text_fields, 
            size: 18, 
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: 8),
          Text(
            'Size',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: FontWeight.w600,
            ),
          ),
          Expanded(
            child: Slider(
              value: _readerFontSize,
              min: 0.8,
              max: 1.6,
              divisions: 8,
              activeColor: theme.colorScheme.primary,
              onChanged: (value) => setState(() => _readerFontSize = value),
            ),
          ),
          Text(
            '${(_readerFontSize * 100).round()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernVerseCard(BibleVerse verse, ThemeData theme, int index, bool isFirst, bool isLast) {
    final verseId = _getVerseId(verse);
    final isSelected = _selectedVerseIds.contains(verseId);
    final isFavorite = _verseService.isFavorite(verseId);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: EdgeInsets.only(
        bottom: isLast ? 100 : 24, // Extra space at bottom for FAB
        top: isFirst ? 8 : 0,
      ),
      child: Material(
        elevation: isSelected ? 4 : 1,
        borderRadius: BorderRadius.circular(16),
      color: isSelected
            ? theme.colorScheme.primaryContainer.withValues(alpha: 0.4)
            : theme.colorScheme.surface,
        shadowColor: theme.colorScheme.primary.withValues(alpha: 0.1),
      child: InkWell(
          borderRadius: BorderRadius.circular(16),
        onTap: _isSelectionMode
            ? () => _toggleVerseSelection(verse)
              : () => _showModernVerseActions(verse, theme),
        onLongPress: () {
          if (!_isSelectionMode) {
            setState(() {
              _isSelectionMode = true;
              _selectedVerseIds.add(verseId);
            });
          } else {
            _toggleVerseSelection(verse);
          }
        },
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected 
                    ? theme.colorScheme.primary.withValues(alpha: 0.3)
                    : theme.colorScheme.outline.withValues(alpha: 0.1),
                width: isSelected ? 2 : 1,
              ),
              gradient: isSelected ? LinearGradient(
                colors: [
                  theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
                  theme.colorScheme.secondaryContainer.withValues(alpha: 0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ) : null,
            ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                // Verse number badge
                if (!_isSelectionMode)
                Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.primaryContainer.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: theme.colorScheme.primary.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Center(
                  child: Text(
                    '${verse.verse}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: isSelected
                              ? theme.colorScheme.onPrimary
                              : theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                  )
                else
                  Checkbox(
                    value: isSelected,
                    onChanged: (_) => _toggleVerseSelection(verse),
                    activeColor: theme.colorScheme.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                const SizedBox(width: 16),
                // Verse text
              Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                  verse.text,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          height: 1.8,
                          fontSize: 16 * _readerFontSize,
                          color: theme.colorScheme.onSurface,
                          letterSpacing: 0.2,
                        ),
                      ),
                      if (!_isSelectionMode) ...[
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            // Quick bookmark button
                            InkWell(
                              onTap: () => _toggleFavorite(verse),
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                decoration: BoxDecoration(
                                  color: isFavorite
                                      ? theme.colorScheme.primaryContainer
                                      : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      isFavorite ? Icons.bookmark : Icons.bookmark_outline,
                                      size: 14,
                                      color: isFavorite 
                                          ? theme.colorScheme.primary
                                          : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                    ),
                                    if (isFavorite) ...[
                                      const SizedBox(width: 4),
                                      Text(
                                        'Saved',
                                        style: theme.textTheme.bodySmall?.copyWith(
                                          color: theme.colorScheme.primary,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            // Quick practice button
                            InkWell(
                              onTap: () => _startKaraoke(verse),
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.play_circle_outline,
                                      size: 14,
                                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Practice',
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const Spacer(),
                            // More options
                            IconButton(
                              icon: Icon(
                                Icons.more_horiz,
                                size: 20,
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                              ),
                              onPressed: () => _showModernVerseActions(verse, theme),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerseCard(BibleVerse verse, ThemeData theme) {
    // Fallback to modern card for search results
    return _buildModernVerseCard(verse, theme, 0, false, false);
  }

  void _showModernVerseActions(BibleVerse verse, ThemeData theme) {
    final verseId = _getVerseId(verse);
    final isFavorite = _verseService.isFavorite(verseId);
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
        ),
        child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
              // Handle bar
            Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
              // Verse reference header
              Container(
                margin: const EdgeInsets.fromLTRB(24, 8, 24, 20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                      theme.colorScheme.secondaryContainer.withValues(alpha: 0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.auto_awesome,
                        color: theme.colorScheme.onPrimary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
              child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${_selectedBook?.name ?? ''} $_selectedChapter:${verse.verse}',
                            style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                          Text(
                            _settingsService.bibleTranslation.code,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // Action buttons
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                child: Column(
                  children: [
                    _buildActionButton(
                      theme,
                      icon: Icons.play_circle_filled,
                      title: 'Start Practice',
                      subtitle: 'Learn with karaoke mode',
                      color: theme.colorScheme.primary,
                    onTap: () {
                      Navigator.pop(context);
                      _startKaraoke(verse);
                    },
                  ),
                    const SizedBox(height: 8),
                    _buildActionButton(
                      theme,
                      icon: isFavorite ? Icons.bookmark_remove : Icons.bookmark,
                      title: isFavorite ? 'Remove from Favorites' : 'Add to Favorites',
                      subtitle: isFavorite ? 'Remove from saved verses' : 'Save for quick access',
                      color: theme.colorScheme.secondary,
                    onTap: () {
                      Navigator.pop(context);
                      _toggleFavorite(verse);
                    },
                  ),
                    const SizedBox(height: 8),
                    _buildActionButton(
                      theme,
                      icon: Icons.copy_all,
                      title: 'Copy Verse',
                      subtitle: 'Copy text to clipboard',
                      color: theme.colorScheme.tertiary,
                    onTap: () {
                      Navigator.pop(context);
                        // TODO: Implement copy to clipboard
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Verse copied to clipboard'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    _buildActionButton(
                      theme,
                      icon: Icons.share,
                      title: 'Share Verse',
                      subtitle: 'Share with friends',
                      color: theme.colorScheme.tertiary,
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Implement share
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Share feature coming soon'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                    },
                  ),
                ],
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    ThemeData theme, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
      borderRadius: BorderRadius.circular(16),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              ),
            ],
          ),
        ),
      ),
    );
  }


  Widget _buildSearchResults(ThemeData theme) {
    if (_searchResults.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: theme.textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final verse = _searchResults[index];
        return _buildVerseCard(verse, theme);
      },
    );
  }

  Widget _buildBottomActions(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isPlayingChapter ? null : _listenToChapter,
              icon: Icon(_isPlayingChapter ? Icons.pause_circle_filled : Icons.play_circle_filled),
              label: Text(_isPlayingChapter ? 'Playing' : 'Listen'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 18),
                elevation: 0,
                backgroundColor: theme.colorScheme.primaryContainer,
                foregroundColor: theme.colorScheme.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _practiceEntireChapter(),
              icon: const Icon(Icons.mic_none_rounded),
              label: const Text('Practice'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 18),
                elevation: 0,
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionActions(ThemeData theme) {
    final sourceList = _isSearching ? _searchResults : _verses;
    final selectedCount = _selectedVerseIds.length;
    final totalCount = sourceList.length;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Selection info and controls
          Row(
            children: [
              Expanded(
                child: Text(
                  '$selectedCount verse${selectedCount == 1 ? '' : 's'} selected',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
              TextButton.icon(
                onPressed: _selectedVerseIds.length == totalCount
                    ? _deselectAllVerses
                    : _selectAllVerses,
                icon: Icon(
                  _selectedVerseIds.length == totalCount
                      ? Icons.deselect
                      : Icons.select_all,
                  size: 18,
                ),
                label: Text(
                  _selectedVerseIds.length == totalCount
                      ? 'Deselect All'
                      : 'Select All',
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: _deselectAllVerses,
                tooltip: 'Cancel',
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _addSelectedToFavorites,
                  icon: const Icon(Icons.star_outline),
                  label: const Text('Add to Favorites'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _practiceSelectedVerses,
                  icon: const Icon(Icons.play_circle_outline),
                  label: const Text('Practice'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _practiceEntireChapter() async {
    if (_verses.isEmpty) return;
    
    final currentUser = await _userService.getCurrentUser();
    if (currentUser == null || !mounted) return;

    final chapterText = _verses.map((v) => v.text).join(' ');
    final verse = _verses.first.toVerse(_selectedBook!.name).copyWith(
      text: chapterText,
      verseNumber: 0,
      id: 'bible_${_selectedBook!.id}_${_selectedChapter}_all',
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: verse,
          userId: currentUser.id,
          progressService: _progressService,
          settingsService: _settingsService,
        ),
      ),
    );
  }
}

