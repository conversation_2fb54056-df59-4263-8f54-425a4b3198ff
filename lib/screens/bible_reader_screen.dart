import 'dart:async';
import 'package:flutter/material.dart';
import 'package:echoverse/models/bible_book.dart';
import 'package:echoverse/models/bible_verse.dart';
import 'package:echoverse/models/user.dart';
import 'package:echoverse/services/bible_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/bible_navigation_service.dart';
import 'package:echoverse/screens/karaoke_screen.dart';

class BibleReaderScreen extends StatefulWidget {
  final BibleBook book;
  final int chapter;
  final ProgressService? progressService;
  final SettingsService? settingsService;
  final BibleNavigationService? navigationService;

  const BibleReaderScreen({
    super.key,
    required this.book,
    required this.chapter,
    this.progressService,
    this.settingsService,
    this.navigationService,
  });

  @override
  State<BibleReaderScreen> createState() => _BibleReaderScreenState();
}

class _BibleReaderScreenState extends State<BibleReaderScreen> {
  final BibleService _bibleService = BibleService();
  final UserService _userService = UserService();
  late final ProgressService _progressService;
  late final SettingsService _settingsService;
  late final BibleNavigationService _navigationService;

  List<BibleVerse> _verses = [];
  bool _isLoading = true;
  User? _currentUser;
  double _readerFontSize = 1.0;
  int _maxChapters = 0;
  bool _isSelectionMode = false;
  final Set<int> _selectedVerseIndices = {};
  final ScrollController _scrollController = ScrollController();
  Timer? _scrollTimer;

  @override
  void initState() {
    super.initState();
    _progressService = widget.progressService ?? ProgressService();
    _settingsService = widget.settingsService ?? SettingsService();
    _navigationService = widget.navigationService ?? BibleNavigationService();
    _setupScrollTracking();
    _initialize();
  }

  void _setupScrollTracking() {
    _scrollController.addListener(() {
      // Debounce scroll position updates
      _scrollTimer?.cancel();
      _scrollTimer = Timer(const Duration(milliseconds: 500), () {
        _navigationService.updateScrollPosition(
          widget.book.id,
          widget.chapter,
          _scrollController.offset,
        );
      });
    });
  }

  @override
  void dispose() {
    // Clean up controllers and timers
    _scrollController.dispose();
    _scrollTimer?.cancel();

    // Clear verse data to help with garbage collection
    _verses.clear();
    _selectedVerseIndices.clear();

    super.dispose();
  }
  
  Future<void> _initialize() async {
    await _settingsService.initialize();
    try {
      await _bibleService.setTranslation(_settingsService.bibleTranslation.code);
    } catch (e) {
      // Fallback to BSB if selected translation fails
      await _bibleService.setTranslation('BSB');
    }
    await _loadChapter();
  }

  Future<void> _loadChapter() async {
    setState(() => _isLoading = true);

    try {
      final verses = await _bibleService.getChapter(widget.book.id, widget.chapter);
      final user = await _userService.getCurrentUser();
      final chapterCount = await _bibleService.getChapterCount(widget.book.id);

      setState(() {
        _verses = verses;
        _currentUser = user;
        _maxChapters = chapterCount;
        _isLoading = false;
      });

      // Update navigation service with current location
      final location = BibleLocation(
        bookId: widget.book.id,
        bookName: widget.book.name,
        chapter: widget.chapter,
      );

      await _navigationService.updateCurrentLocation(
        location,
        context: NavigationContext.directNavigation,
      );

      // Restore scroll position after a short delay
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _restoreScrollPosition();
      });

    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading chapter: $e')),
        );
      }
    }
  }

  void _restoreScrollPosition() {
    final currentLocation = _navigationService.currentLocation;
    if (currentLocation != null &&
        currentLocation.bookId == widget.book.id &&
        currentLocation.chapter == widget.chapter &&
        currentLocation.scrollPosition > 0) {

      _scrollController.animateTo(
        currentLocation.scrollPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedVerseIndices.clear();
      }
    });
  }

  void _toggleVerseSelection(int index) {
    setState(() {
      if (_selectedVerseIndices.contains(index)) {
        _selectedVerseIndices.remove(index);
      } else {
        _selectedVerseIndices.add(index);
      }
    });
  }

  void _navigateToKaraoke(BibleVerse verse) {
    if (_currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to practice verses')),
      );
      return;
    }
    
    final verseForPractice = verse.toVerse(widget.book.name);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: verseForPractice,
          userId: _currentUser!.id,
          progressService: _progressService,
          settingsService: _settingsService,
        ),
      ),
    );
  }

  void _practiceSelectedVerses() {
    if (_currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to practice verses')),
      );
      return;
    }

    List<BibleVerse> versesToPractice;
    
    if (_isSelectionMode && _selectedVerseIndices.isNotEmpty) {
      // Practice selected verses
      versesToPractice = _selectedVerseIndices
          .map((index) => _verses[index])
          .toList()
        ..sort((a, b) => a.verse.compareTo(b.verse));
    } else {
      // Practice entire chapter
      versesToPractice = _verses;
    }

    if (versesToPractice.isEmpty) return;

    // Combine selected verses into one text
    final combinedText = versesToPractice
        .map((v) => '${v.verse}. ${v.text}')
        .join(' ');
    
    // Get first verse for reference
    final firstVerse = versesToPractice.first;
    
    // Create a combined verse object
    final combinedVerse = firstVerse.toVerse(widget.book.name).copyWith(
      text: combinedText,
      verseNumber: firstVerse.verse,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: combinedVerse,
          userId: _currentUser!.id,
          progressService: _progressService,
          settingsService: _settingsService,
        ),
      ),
    ).then((_) {
      // Clear selection after practicing
      setState(() {
        _isSelectionMode = false;
        _selectedVerseIndices.clear();
      });
    });
  }

  void _navigateToPreviousChapter() async {
    if (widget.chapter > 1) {
      final newChapter = widget.chapter - 1;

      // Update navigation service
      final location = BibleLocation(
        bookId: widget.book.id,
        bookName: widget.book.name,
        chapter: newChapter,
      );

      await _navigationService.updateCurrentLocation(
        location,
        context: NavigationContext.directNavigation,
      );

      if (!mounted) return;

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => BibleReaderScreen(
            book: widget.book,
            chapter: newChapter,
            progressService: _progressService,
            settingsService: _settingsService,
            navigationService: _navigationService,
          ),
        ),
      );
    }
  }

  void _navigateToNextChapter() async {
    if (widget.chapter < _maxChapters) {
      final newChapter = widget.chapter + 1;

      // Update navigation service
      final location = BibleLocation(
        bookId: widget.book.id,
        bookName: widget.book.name,
        chapter: newChapter,
      );

      await _navigationService.updateCurrentLocation(
        location,
        context: NavigationContext.directNavigation,
      );

      if (!mounted) return;

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => BibleReaderScreen(
            book: widget.book,
            chapter: newChapter,
            progressService: _progressService,
            settingsService: _settingsService,
            navigationService: _navigationService,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${widget.book.name} ${widget.chapter}',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isSelectionMode ? Icons.close : Icons.check_circle_outline,
              color: _isSelectionMode 
                ? theme.colorScheme.error 
                : theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            onPressed: _toggleSelectionMode,
            tooltip: _isSelectionMode ? 'Cancel Selection' : 'Select Verses',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: theme.colorScheme.primary))
          : Stack(
              children: [
                // Main content - verses
                SingleChildScrollView(
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.fromLTRB(0, 0, 0, 100),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Use ListView.builder for better performance with large chapters
                      for (int index = 0; index < _verses.length; index++)
                        _buildOptimizedVerseWidget(_verses[index], theme, index),
                    ],
                  ),
                ),
                // Floating font control
                Positioned(
                  bottom: 120,
                  right: 16,
                  child: _buildFloatingFontControl(theme),
                ),
                // Bottom action bar
                _buildBottomActions(theme),
              ],
            ),
    );
  }

  Widget _buildOptimizedVerseWidget(BibleVerse verse, ThemeData theme, int index) {
    final isSelected = _selectedVerseIndices.contains(index);
    
    return GestureDetector(
      onTap: () {
        if (_isSelectionMode) {
          // In selection mode: toggle selection
          _toggleVerseSelection(index);
        } else {
          // In normal mode: enter selection mode and select this verse
          setState(() {
            _isSelectionMode = true;
            _selectedVerseIndices.add(index);
          });
        }
      },
      onLongPress: () {
        // Long press: practice immediately (quick action)
        if (!_isSelectionMode) {
          _navigateToKaraoke(verse);
        } else {
          // In selection mode: toggle selection
          _toggleVerseSelection(index);
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primaryContainer.withValues(alpha: 0.2)
            : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
              ? theme.colorScheme.primary.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.06),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced verse number or selection indicator
            if (_isSelectionMode)
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Icon(
                  isSelected ? Icons.check : Icons.circle_outlined,
                  size: 20,
                  color: isSelected
                    ? Colors.white
                    : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              )
            else
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Center(
                  child: Text(
                    '${verse.verse}',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            const SizedBox(width: 16),
            // Enhanced verse text with better typography
            Expanded(
              child: Text(
                verse.text,
                style: theme.textTheme.bodyLarge?.copyWith(
                  height: 1.7,
                  fontSize: 18 * _readerFontSize,
                  letterSpacing: 0.1,
                  fontWeight: FontWeight.w400,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingFontControl(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.12),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.04),
            blurRadius: 24,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Font size increase button
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.text_increase, size: 22),
              color: theme.colorScheme.primary,
              onPressed: () {
                if (_readerFontSize < 1.4) {
                  setState(() => _readerFontSize += 0.1);
                }
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
            ),
          ),
          const SizedBox(height: 8),
          // Font size indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${(_readerFontSize * 100).round()}%',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.secondary,
                fontWeight: FontWeight.w600,
                fontSize: 11,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Font size decrease button
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.text_decrease, size: 22),
              color: theme.colorScheme.primary,
              onPressed: () {
                if (_readerFontSize > 0.8) {
                  setState(() => _readerFontSize -= 0.1);
                }
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(ThemeData theme) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        padding: const EdgeInsets.fromLTRB(20, 12, 20, 12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.12),
              width: 1,
            ),
          ),
        ),
        child: SafeArea(
          top: false,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBottomAction(
                theme, 
                Icons.chevron_left, 
                'Previous',
                widget.chapter > 1 ? _navigateToPreviousChapter : null,
              ),
              _buildBottomAction(
                theme, 
                Icons.play_circle_outline, 
                _isSelectionMode && _selectedVerseIndices.isNotEmpty
                  ? 'Practice (${_selectedVerseIndices.length})'
                  : 'Practice',
                _practiceSelectedVerses,
              ),
              _buildBottomAction(
                theme, 
                Icons.share_outlined, 
                'Share',
                () {},
              ),
              _buildBottomAction(
                theme, 
                Icons.chevron_right, 
                'Next',
                widget.chapter < _maxChapters ? _navigateToNextChapter : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildBottomAction(ThemeData theme, IconData icon, String label, VoidCallback? onTap) {
    final isEnabled = onTap != null;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 24,
              color: isEnabled 
                ? theme.colorScheme.primary 
                : theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isEnabled 
                  ? theme.colorScheme.onSurface.withValues(alpha: 0.8)
                  : theme.colorScheme.onSurface.withValues(alpha: 0.3),
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

}
