import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

import 'package:echoverse/models/verse.dart';
import 'package:echoverse/services/audio_service.dart';
import 'package:echoverse/services/music_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/widgets/audio_controls.dart';
import 'package:echoverse/widgets/karaoke_word.dart';
import 'package:echoverse/widgets/repeat_word.dart';

class _RepeatSession {
  final DateTime timestamp;
  final int matchedWords;
  final int totalWords;
  final double accuracy;
  final double wordsPerMinute;
  final Duration elapsed;
  final List<String> focusWordsSnapshot;
  final String transcript;

  const _RepeatSession({
    required this.timestamp,
    required this.matchedWords,
    required this.totalWords,
    required this.accuracy,
    required this.wordsPerMinute,
    required this.elapsed,
    required this.focusWordsSnapshot,
    required this.transcript,
  });
}

class _RepeatEvaluation {
  final int matchedCount;
  final Set<int> matchedIndices;
  final List<String> focusWords;
  final int longestStreak;

  const _RepeatEvaluation({
    required this.matchedCount,
    required this.matchedIndices,
    required this.focusWords,
    required this.longestStreak,
  });

  const _RepeatEvaluation.empty()
      : matchedCount = 0,
        matchedIndices = const <int>{},
        focusWords = const [],
        longestStreak = 0;
}

enum PracticeMode { listen, repeat, sayAfter, music }

enum RepeatPhase {
  study,      // Show verse for memorization
  reciting,   // Hide verse and record recitation
  results,    // Show results after recitation
}

enum ChunkState {
  pending,    // Not yet reached
  playing,    // Audio is playing
  listening,  // Waiting for user to speak
  correct,    // User said it correctly
  incorrect,  // User said it incorrectly (will retry)
}

class VerseChunk {
  final List<String> words;
  final int startIndex;
  final int endIndex;
  ChunkState state;
  int retryCount;
  
  VerseChunk({
    required this.words,
    required this.startIndex,
    required this.endIndex,
    this.state = ChunkState.pending,
    this.retryCount = 0,
  });
  
  String get text => words.join(' ');
}

class KaraokeScreen extends StatefulWidget {
  final Verse verse;
  final String userId;
  final ProgressService progressService;
  final SettingsService settingsService;

  const KaraokeScreen(
      {super.key,
      required this.verse,
      required this.userId,
      required this.progressService,
      required this.settingsService});

  @override
  State<KaraokeScreen> createState() => _KaraokeScreenState();
}

class _KaraokeScreenState extends State<KaraokeScreen> {
  static const double _repeatAccuracyThreshold = 0.65;
  static const String _webSpeechUnsupportedMessage =
      'Speech recognition requires microphone permission. Please allow microphone access in your browser when prompted.';

  final AudioService _audioService = AudioService();
  final MusicService _musicService = MusicService();
  final SpeechToText _speechToText = SpeechToText();

  PracticeMode _selectedMode = PracticeMode.listen;

  late final List<String> _words;
  late final List<String> _normalizedWords;
  int _currentWordIndex = -1;
  bool _isPlaying = false;
  int _practiceCount = 0;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  // Music mode state
  bool _isMusicPlaying = false;
  bool _isGeneratingMusic = false;
  Duration _musicPosition = Duration.zero;
  Duration _musicDuration = Duration.zero;
  MusicGenre _selectedGenre = MusicGenre.gospelPop;
  int _musicDurationSeconds = 10; // Default to 10 seconds as in example

  bool _speechAvailable = false;
  bool _isListening = false;
  int _repeatMatchedWords = 0;
  
  // Say After mode state
  List<VerseChunk> _sayAfterChunks = [];
  int _currentChunkIndex = 0;
  int _sayAfterChunkSize = 3; // default 3 words per chunk
  bool _isSayAfterPlaying = false;
  bool _isSayAfterListening = false;
  String _sayAfterTranscript = '';
  Timer? _sayAfterTimer;
  // Gap between audio play and start-listening (milliseconds)
  int _sayAfterGapMs = 500;
  double _repeatAccuracy = 0.0;
  // ignore: unused_field
  Duration _repeatElapsed =
      Duration.zero; // Tracked internally but not displayed in simplified UI
  DateTime? _repeatStart;
  Timer? _repeatTimer;
  String _repeatTranscript = '';
  String? _speechError;
  int? _repeatCountdown;
  Timer? _repeatCountdownTimer;
  double _repeatWordsPerMinute = 0.0;
  String? _repeatFeedback;
  Set<int> _repeatMatchedWordIndices = <int>{};
  Set<int> _repeatIncorrectWordIndices = <int>{}; // Words that were wrong
  List<String> _repeatFocusWords = const [];
  int _repeatLongestStreak = 0;
  bool _isRepeatCompleted = false;
  RepeatPhase _repeatPhase = RepeatPhase.study;
  List<_RepeatSession> _repeatHistory = [];
  double _repeatBestAccuracy = 0.0;
  double _repeatAverageAccuracy = 0.0;


  // Diagnostics for mic input and transient retry
  int _noSpeechRetryCount = 0;
  int? _lastLevelLogMs;
  Timer? _autoRestartTimer;
  bool _stopInProgress = false;
  static const Duration _autoRestartDelay = Duration(seconds: 2);

  @override
  void initState() {
    super.initState();
    _words = widget.verse.text.split(RegExp(r'\s+'));
    _normalizedWords = _words.map(_normalizeWord).toList();
    final existingProgress = widget.progressService
        .getProgressForVerse(widget.userId, widget.verse.id);
    _practiceCount = existingProgress?.practiceCount ?? 0;
    _audioService.setSpeechRate(widget.settingsService.audioSpeed);
    _audioService.setVoiceId(widget.settingsService.voiceType.id);
    _musicService.setCallbacks(
      onPlayingStateChanged: (playing) {
        if (!mounted) return;
        setState(() => _isMusicPlaying = playing);
      },
      onPositionChanged: (position) {
        if (!mounted) return;
        setState(() => _musicPosition = position);
      },
      onDurationChanged: (duration) {
        if (!mounted) return;
        setState(() => _musicDuration = duration);
      },
    );
    // Initialize speech recognition on all platforms including web
    if (!kIsWeb) {
      // On mobile, initialize immediately
      _initializeSpeech();
    }
    // On web, speech_to_text will request permission when first used
  }

  @override
  void dispose() {
    _repeatTimer?.cancel();
    _repeatCountdownTimer?.cancel();
    _autoRestartTimer?.cancel();
    _sayAfterTimer?.cancel();
    _audioService.dispose();
    _musicService.dispose();
    _speechToText.stop();
    _speechToText.cancel();
    super.dispose();
  }

  Future<void> _playVerse() async {
    if (_selectedMode != PracticeMode.listen) return;
    if (_isPlaying) {
      await _audioService.pause();
    } else if (_currentPosition == Duration.zero ||
        _currentPosition >= _totalDuration) {
      _audioService.setSpeechRate(widget.settingsService.audioSpeed);
      _audioService.setVoiceId(widget.settingsService.voiceType.id);
      await _audioService.speakVerse(
        widget.verse.text,
        onWord: (index) {
          if (mounted) setState(() => _currentWordIndex = index);
        },
        onPlayingStateChanged: (playing) {
          if (mounted) {
            setState(() => _isPlaying = playing);
            if (!playing &&
                _currentPosition >= _totalDuration &&
                _totalDuration > Duration.zero) {
              unawaited(_registerPractice());
            }
          }
        },
        onPositionChanged: (position) {
          if (mounted) setState(() => _currentPosition = position);
        },
        onDurationChanged: (duration) {
          if (mounted) setState(() => _totalDuration = duration);
        },
      );
    } else {
      await _audioService.resume();
    }
  }

  Future<void> _replay() async {
    await _audioService.stop();
    setState(() {
      _currentWordIndex = -1;
      _isPlaying = false;
      _currentPosition = Duration.zero;
    });
    await Future.delayed(const Duration(milliseconds: 200));
    _audioService.setSpeechRate(widget.settingsService.audioSpeed);
    _audioService.setVoiceId(widget.settingsService.voiceType.id);
    await _audioService.speakVerse(
      widget.verse.text,
      onWord: (index) {
        if (mounted) setState(() => _currentWordIndex = index);
      },
      onPlayingStateChanged: (playing) {
        if (mounted) {
          setState(() => _isPlaying = playing);
          if (!playing &&
              _currentPosition >= _totalDuration &&
              _totalDuration > Duration.zero) {
            unawaited(_registerPractice());
          }
        }
      },
      onPositionChanged: (position) {
        if (mounted) setState(() => _currentPosition = position);
      },
      onDurationChanged: (duration) {
        if (mounted) setState(() => _totalDuration = duration);
      },
    );
  }

  void _onSeek(Duration position) {
    _audioService.seek(position);
  }

  void _onRewind() {
    final newPosition = _currentPosition - const Duration(seconds: 10);
    _audioService
        .seek(newPosition < Duration.zero ? Duration.zero : newPosition);
  }

  void _onNextLine() {
    // Skip forward 10 seconds
    final newPosition = _currentPosition + const Duration(seconds: 10);
    if (newPosition > _totalDuration) {
      _audioService.seek(_totalDuration);
    } else {
      _audioService.seek(newPosition);
    }
  }

  void _onSpeedChanged(double speed) {
    widget.settingsService.setAudioSpeed(speed);
    _audioService.setSpeechRate(speed);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final fadeLevel = _calculateFadeLevel();
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildEnhancedAppBar(theme),
      body: SafeArea(
        child: Column(
          children: [
            // Mode Selector
            _buildEnhancedModeSelector(theme),
            
            // Mode Description
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
              child: Text(
                _modeDescription(_selectedMode),
                textAlign: TextAlign.center,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontSize: 12,
                ),
              ),
            ),
            
            // Main Content Area
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildEnhancedVerseContainer(theme, fadeLevel),
                    const SizedBox(height: 20),
                    _buildModeSpecificContent(theme),
                  ],
                ),
              ),
            ),
            
            // Bottom Controls
            _buildEnhancedBottomControls(theme, fadeLevel),
          ],
        ),
      ),
    );
  }





  Widget _buildRepeatVerse(ThemeData theme) {
    // In reciting phase, hide the verse completely (flashcard-style)
    if (_repeatPhase == RepeatPhase.reciting) {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated microphone icon
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.8, end: 1.2),
              duration: const Duration(milliseconds: 1000),
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(32),
                    ),
                    child: Icon(
                      Icons.mic,
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              'Reciting from memory...',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'The verse is hidden. Speak what you remember.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // In study and results phases, show the verse
    return Wrap(
      spacing: 4,
      runSpacing: 6,
      children: _words.asMap().entries.map((entry) {
        final index = entry.key;
        final word = entry.value;

        WordMatchState state;
        bool isBlurred = false;

        if (_repeatPhase == RepeatPhase.results) {
          // In results phase, show all results
          if (_repeatMatchedWordIndices.contains(index)) {
            state = WordMatchState.correct;
          } else {
            // Words not matched are "missed"
            state = WordMatchState.missed;
          }
        } else {
          // In study phase, show all words as normal
          state = WordMatchState.pending;
        }

        return RepeatWord(
          word: word,
          state: state,
          isBlurred: isBlurred,
        );
      }).toList(),
    );
  }

  Widget _buildEnhancedRepeatControls(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Phase-specific content
        if (_repeatPhase == RepeatPhase.study)
          _buildStudyPhaseControls(theme)
        else if (_repeatPhase == RepeatPhase.reciting)
          _buildRecitingPhaseControls(theme)
        else if (_repeatPhase == RepeatPhase.results)
          _buildResultsPhaseControls(theme),
      ],
    );
  }

  Widget _buildStudyPhaseControls(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Study phase instructions
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.menu_book, color: theme.colorScheme.primary, size: 20),
                  const SizedBox(width: 10),
                  Text(
                    'Study the verse',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Take your time to memorize the verse. When ready, tap "Start Reciting" to begin.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.secondary.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.visibility_off,
                      color: theme.colorScheme.secondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Flashcard Mode: The verse will be completely hidden during recitation',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.secondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Start reciting button
        ElevatedButton.icon(
          onPressed: _startRecitingPhase,
          icon: const Icon(Icons.mic, size: 20),
          label: const Text('Start Reciting'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildRecitingPhaseControls(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Enhanced recording indicator with flashcard reminder
        Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.red.withValues(alpha: 0.1),
                Colors.red.withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.red.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.fiber_manual_record, color: Colors.red, size: 20),
                  const SizedBox(width: 10),
                  Text(
                    'Recording...',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Sound level indicator
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: _currentSoundLevel,
                  minHeight: 8,
                  backgroundColor: Colors.grey.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _currentSoundLevel > 0.5 ? Colors.red : Colors.red.withValues(alpha: 0.5),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Recite the verse from memory',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // Live transcript display - always show during recording
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.hearing,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'What I\'m hearing:',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _repeatTranscript.isEmpty ? 'Listening... start speaking' : '"$_repeatTranscript"',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: _repeatTranscript.isEmpty
                        ? theme.colorScheme.onSurface.withValues(alpha: 0.5)
                        : theme.colorScheme.onSurface,
                  ),
                ),
              ),
              if (_repeatTranscript.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    'Matched ${_repeatMatchedWords} of ${_normalizedWords.length} words',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Stop recording button
        ElevatedButton.icon(
          onPressed: _stopRecitingPhase,
          icon: const Icon(Icons.stop, size: 20),
          label: const Text('Stop Recording'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.error,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsPhaseControls(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Results display
        Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.emoji_events,
                      color: theme.colorScheme.secondary, size: 24),
                  const SizedBox(width: 10),
                  Text(
                    'Accuracy: ${(100 * _repeatAccuracy).toStringAsFixed(0)}%',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '$_repeatMatchedWords / ${_normalizedWords.length} words',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),

        // Transcript comparison
        if (_repeatTranscript.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.transcribe,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'What you said:',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '"$_repeatTranscript"',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.menu_book,
                      size: 16,
                      color: theme.colorScheme.secondary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Original verse:',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.secondary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '"${widget.verse.text}"',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Compare your recitation with the original to see pronunciation differences.',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _restartStudyPhase,
                icon: const Icon(Icons.refresh, size: 20),
                label: const Text('Try Again'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _showVerseAgain,
                icon: const Icon(Icons.visibility, size: 20),
                label: const Text('Show Verse'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
              ),
            ),
          ],
        ),

        // Error message
        if (_speechError != null)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline,
                      size: 16, color: theme.colorScheme.error),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _speechError!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildModeSpecificContent(ThemeData theme) {
    switch (_selectedMode) {
      case PracticeMode.listen:
        return Text(
          'Tap play to hear the verse with synchronized highlighting. Replays count toward your practice progress.',
          style: theme.textTheme.bodyMedium,
        );
      case PracticeMode.repeat:
        return Text(
          'Study the verse, then recite from memory. The verse will be hidden during recitation for a true test of memorization.',
          style: theme.textTheme.bodyMedium,
        );
      case PracticeMode.sayAfter:
        return Text(
          'The app will read small chunks of the verse. After each chunk, repeat what you heard. Perfect for memorization!',
          style: theme.textTheme.bodyMedium,
        );
      case PracticeMode.music:
        return Text(
            'Generate beautiful instrumental music inspired by this scripture. Choose a style and let AI create unique music for meditation and reflection.',
            style: theme.textTheme.bodyMedium);
    }
  }

  Widget _buildModeControls(ThemeData theme) {
    switch (_selectedMode) {
      case PracticeMode.listen:
        return AudioControls(
          isPlaying: _isPlaying,
          onPlayPause: _playVerse,
          onReplay: _replay,
          onRewind: _onRewind,
          onNextLine: _onNextLine,
          currentPosition: _currentPosition,
          totalDuration: _totalDuration,
          onSeek: _onSeek,
          speed: widget.settingsService.audioSpeed,
          onSpeedChanged: _onSpeedChanged,
        );
      case PracticeMode.repeat:
        return _buildEnhancedRepeatControls(theme);
      case PracticeMode.sayAfter:
        return _buildSayAfterControls(theme);
      case PracticeMode.music:
        return const SizedBox.shrink(); // No extra controls needed for music mode
    }
  }

  // Unused method - kept for potential future use
  // ignore: unused_element
  Widget _buildRepeatStats(ThemeData theme) {
    final confidenceColor = _confidenceColor(theme, _repeatAccuracy);
    final confidenceLabel = _confidenceLabel(_repeatAccuracy);
    final tempoLabel = _repeatWordsPerMinute > 0
        ? '${_repeatWordsPerMinute.toStringAsFixed(0)} wpm'
        : 'warming up';
    final tempoDescriptor = _tempoDescriptor(_repeatWordsPerMinute);
    final feedback =
        _repeatFeedback ?? 'Tap Start Reciting to begin your guided practice.';
    final nextSegment = _nextRepeatSegment();
    final streakLabel = _repeatLongestStreak > 0
        ? '${_repeatLongestStreak} words'
        : 'warming up';
    final hasHistory = _repeatHistory.isNotEmpty;
    final bestAccuracyText =
        hasHistory ? '${(_repeatBestAccuracy * 100).toStringAsFixed(0)}%' : '–';
    final averageAccuracyText = hasHistory
        ? '${(_repeatAverageAccuracy * 100).toStringAsFixed(0)}%'
        : '–';
    final transcriptPreview = _repeatTranscript.isEmpty
        ? 'Start reciting to capture your words.'
        : _repeatTranscript;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.graphic_eq, color: confidenceColor),
            const SizedBox(width: 8),
            Text('Accuracy ${(100 * _repeatAccuracy).toStringAsFixed(0)}%',
                style: theme.textTheme.bodyMedium
                    ?.copyWith(fontWeight: FontWeight.w600)),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildInfoPill(theme, Icons.insights, confidenceColor,
                'Confidence $confidenceLabel'),
            _buildInfoPill(theme, Icons.speed, theme.colorScheme.secondary,
                'Tempo $tempoLabel'),
            if (_repeatMatchedWordIndices.isNotEmpty ||
                _repeatLongestStreak > 0)
              _buildInfoPill(theme, Icons.auto_graph, theme.colorScheme.primary,
                  'Streak $streakLabel'),
          ],
        ),
        if (_repeatWordsPerMinute > 0)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Flow $tempoDescriptor',
              style: theme.textTheme.labelMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6)),
            ),
          ),
        if (hasHistory)
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Text(
              'Best run $bestAccuracyText • Average $averageAccuracyText',
              style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6)),
            ),
          ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Coaching',
                  style: theme.textTheme.labelMedium?.copyWith(
                      color:
                          theme.colorScheme.onSurface.withValues(alpha: 0.6))),
              const SizedBox(height: 4),
              Text(
                feedback,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        if (_repeatFocusWords.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.bolt_rounded,
                        size: 18, color: theme.colorScheme.primary),
                    const SizedBox(width: 6),
                    Text('Focus words',
                        style: theme.textTheme.labelMedium?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.6))),
                  ],
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _repeatFocusWords
                      .map((word) => _buildWordChip(theme, word))
                      .toList(),
                ),
                const SizedBox(height: 10),
                Text(
                  'Reinforce these before you move on—they’re the words the mic missed.',
                  style: theme.textTheme.labelSmall?.copyWith(
                      color:
                          theme.colorScheme.onSurface.withValues(alpha: 0.6)),
                ),
              ],
            ),
          ),
        if (_repeatFocusWords.isNotEmpty) const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Next up',
                  style: theme.textTheme.labelMedium?.copyWith(
                      color:
                          theme.colorScheme.onSurface.withValues(alpha: 0.6))),
              const SizedBox(height: 4),
              Text(
                nextSegment,
                style: theme.textTheme.bodyMedium
                    ?.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        if (hasHistory)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Recent sessions',
                        style: theme.textTheme.labelMedium?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.6))),
                    Text(
                      _repeatHistory.length == 1
                          ? 'Latest'
                          : '${_repeatHistory.length} runs',
                      style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.5)),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ..._repeatHistory
                    .map((session) => _buildRepeatHistoryRow(theme, session)),
              ],
            ),
          ),
        if (hasHistory) const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Latest transcript',
                  style: theme.textTheme.labelMedium?.copyWith(
                      color:
                          theme.colorScheme.onSurface.withValues(alpha: 0.6))),
              const SizedBox(height: 4),
              Text(
                transcriptPreview,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoPill(
      ThemeData theme, IconData icon, Color color, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.14),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            label,
            style: theme.textTheme.labelMedium
                ?.copyWith(color: color, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildWordChip(ThemeData theme, String word) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Text(
        word,
        style: theme.textTheme.labelMedium?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildRepeatHistoryRow(ThemeData theme, _RepeatSession session) {
    final accuracyText = '${(session.accuracy * 100).toStringAsFixed(0)}%';
    final wpmText = session.wordsPerMinute > 0
        ? '${session.wordsPerMinute.toStringAsFixed(0)} wpm'
        : 'warming up';
    final timestampLabel = _relativeTimeLabel(session.timestamp);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondary.withValues(alpha: 0.16),
              borderRadius: BorderRadius.circular(14),
            ),
            child: Text(
              accuracyText,
              style: theme.textTheme.labelLarge?.copyWith(
                color: theme.colorScheme.secondary,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${session.matchedWords}/${session.totalWords} words • ${_formatDuration(session.elapsed)}',
                  style: theme.textTheme.bodySmall
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 2),
                Text(
                  'Tempo $wpmText • $timestampLabel',
                  style: theme.textTheme.labelSmall?.copyWith(
                      color:
                          theme.colorScheme.onSurface.withValues(alpha: 0.6)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMusicMode(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          '🎵 Music inspired by this verse',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        
        // Genre/Style selector
        Text(
          'Music Style',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: MusicGenre.values.map((genre) {
            final isSelected = _selectedGenre == genre;
            return FilterChip(
              label: Text(genre.displayName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() => _selectedGenre = genre);
                }
              },
              selectedColor: theme.colorScheme.primaryContainer,
              checkmarkColor: theme.colorScheme.primary,
            );
          }).toList(),
        ),
        const SizedBox(height: 20),
        
        // Duration slider
        Row(
          children: [
            Text('Duration', style: theme.textTheme.titleSmall),
            const Spacer(),
            Text(
              '${_musicDurationSeconds}s',
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        Slider(
          value: _musicDurationSeconds.toDouble(),
          min: 10,
          max: 300,
          divisions: 29,
          label: '${_musicDurationSeconds}s',
          onChanged: (value) {
            setState(() => _musicDurationSeconds = value.toInt());
          },
        ),
        const SizedBox(height: 20),
        
        // Generate button
        ElevatedButton.icon(
          onPressed: _isGeneratingMusic ? null : _generateMusic,
          icon: _isGeneratingMusic
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.music_note),
          label: Text(_isGeneratingMusic ? 'Generating...' : 'Generate Music'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        
        // Music player (if music is generated)
        if (_musicDuration.inSeconds > 0) ...[
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                // Progress bar
                Row(
                  children: [
                    Text(
                      _formatDuration(_musicPosition),
                      style: theme.textTheme.bodySmall,
                    ),
                    Expanded(
                      child: Slider(
                        value: _musicPosition.inSeconds.toDouble(),
                        max: _musicDuration.inSeconds.toDouble(),
                        onChanged: (value) {
                          _musicService.seek(Duration(seconds: value.toInt()));
                        },
                      ),
                    ),
                    Text(
                      _formatDuration(_musicDuration),
                      style: theme.textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                // Controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      onPressed: () => _musicService.seek(Duration.zero),
                      icon: const Icon(Icons.replay),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        onPressed: () {
                          if (_isMusicPlaying) {
                            _musicService.pause();
                          } else {
                            _musicService.play();
                          }
                        },
                        icon: Icon(
                          _isMusicPlaying ? Icons.pause : Icons.play_arrow,
                          color: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    IconButton(
                      onPressed: () => _musicService.stop(),
                      icon: const Icon(Icons.stop),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _generateMusic() async {
    setState(() => _isGeneratingMusic = true);
    
    try {
      // Ensure we use the exact verse text without any modifications
      final exactVerseText = widget.verse.text.trim();
      
      if (exactVerseText.isEmpty) {
        if (mounted) {
          _showSnackBar('Verse text is empty');
        }
        return;
      }
      
      // Pass the exact, unmodified verse text to ensure verbatim citation
      await _musicService.generateMusic(
        verseText: exactVerseText, // Exact verse text, no modifications
        verseReference: widget.verse.reference,
        genre: _selectedGenre,
        durationSeconds: _musicDurationSeconds,
      );
    } catch (e) {
      if (mounted) {
        _showSnackBar('Failed to generate music: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingMusic = false);
      }
    }
  }


  void _changeMode(PracticeMode mode) async {
    if (_selectedMode == mode) return;
    if (_isPlaying) {
      await _audioService.stop();
    }
    if (_isListening) {
      await _stopRepeatListening(saveResult: false);
      // Extra safety: ensure speech recognition is fully stopped
      if (_speechToText.isListening) {
        await _speechToText.stop();
      }
    }
    if (_repeatCountdown != null) {
      await _cancelRepeatCountdown();
    }
    
    // Stop Say After mode if active
    if (_selectedMode == PracticeMode.sayAfter) {
      await _stopSayAfterMode();
    }
    
    setState(() {
      _selectedMode = mode;
      _isPlaying = false;
      _currentWordIndex = -1;
      _currentPosition = Duration.zero;
      _totalDuration = Duration.zero;

      // Reset repeat phase when switching to repeat mode
      if (mode == PracticeMode.repeat) {
        _repeatPhase = RepeatPhase.study;
        _repeatMatchedWordIndices.clear();
        _repeatMatchedWords = 0;
        _repeatAccuracy = 0.0;
        _repeatTranscript = '';
        _speechError = null;
        _isRepeatCompleted = false;
      }
    });

    // Initialize speech recognition when switching to Repeat mode
    if (mode == PracticeMode.repeat && !_speechAvailable) {
      await _initializeSpeech();
    }
    // Don't auto-start listening in the new UX - user must click "Start Reciting"
    if (mode != PracticeMode.repeat) {
      _autoRestartTimer?.cancel();
    }
    
    // Initialize Say After mode
    if (mode == PracticeMode.sayAfter) {
      _initializeSayAfterMode();
    }
  }

  Future<void> _ensureRepeatListening() async {
    print('🎤 _ensureRepeatListening called');
    _autoRestartTimer?.cancel();
    if (!_speechAvailable) {
      print('🎤 Speech not available, initializing...');
      await _initializeSpeech();
    }
    if (!_speechAvailable) {
      print('🎤 ❌ Speech still not available after init');
      return;
    }
    if (_isListening) {
      print('🎤 Already listening, skipping');
      return;
    }
    print('🎤 Calling _startRepeatListening...');
    await _startRepeatListening();
  }

  void _prepareForNextRepeatAttempt({String? feedbackMessage}) {
    if (!mounted) return;
    setState(() {
      _isListening = false;
      _repeatMatchedWords = 0;
      _repeatAccuracy = 0.0;
      _repeatWordsPerMinute = 0.0;
      _repeatMatchedWordIndices = <int>{};
      _repeatIncorrectWordIndices = <int>{};
      _repeatFocusWords = const [];
      _repeatLongestStreak = 0;
      _repeatTranscript = '';
      _repeatElapsed = Duration.zero;
      _repeatFeedback = feedbackMessage;
      _isRepeatCompleted = false;
    });
  }

  void _scheduleRepeatAutoRestart() {
    _autoRestartTimer?.cancel();
    if (_selectedMode != PracticeMode.repeat) return;
    if (!_speechAvailable) return;
    _autoRestartTimer = Timer(_autoRestartDelay, () {
      if (!mounted ||
          _selectedMode != PracticeMode.repeat ||
          _isListening ||
          kIsWeb ||
          !_speechAvailable) {
        return;
      }
      _prepareForNextRepeatAttempt(
        feedbackMessage: 'Listening for your next attempt...',
      );
      unawaited(_startRepeatListening());
    });
  }

  // Phase transition methods for improved repeat mode UX
  void _startRecitingPhase() {
    setState(() {
      _repeatPhase = RepeatPhase.reciting;
      _repeatMatchedWordIndices.clear();
      _repeatMatchedWords = 0;
      _repeatAccuracy = 0.0;
      _repeatTranscript = '';
      _speechError = null;
    });

    // Start speech recognition
    unawaited(_startRepeatListening());
  }

  void _stopRecitingPhase() {
    unawaited(_stopRepeatListening());

    setState(() {
      _repeatPhase = RepeatPhase.results;
      _isRepeatCompleted = true;
    });

    // Calculate final results
    _calculateRepeatResults();
  }

  void _restartStudyPhase() {
    setState(() {
      _repeatPhase = RepeatPhase.study;
      _repeatMatchedWordIndices.clear();
      _repeatMatchedWords = 0;
      _repeatAccuracy = 0.0;
      _repeatTranscript = '';
      _speechError = null;
      _isRepeatCompleted = false;
    });
  }

  void _showVerseAgain() {
    setState(() {
      _repeatPhase = RepeatPhase.study;
    });
  }

  void _calculateRepeatResults() {
    // This method can be enhanced to provide more detailed feedback
    final accuracy = _normalizedWords.isNotEmpty
        ? _repeatMatchedWords / _normalizedWords.length
        : 0.0;

    setState(() {
      _repeatAccuracy = accuracy;
    });

    // Add to history for tracking progress
    _repeatHistory.add(_RepeatSession(
      timestamp: DateTime.now(),
      matchedWords: _repeatMatchedWords,
      totalWords: _normalizedWords.length,
      accuracy: accuracy,
      wordsPerMinute: _repeatWordsPerMinute,
      elapsed: Duration.zero, // Could track actual elapsed time if needed
      focusWordsSnapshot: List.from(_repeatFocusWords),
      transcript: _repeatTranscript,
    ));

    // Update best and average accuracy
    if (accuracy > _repeatBestAccuracy) {
      _repeatBestAccuracy = accuracy;
    }

    if (_repeatHistory.isNotEmpty) {
      _repeatAverageAccuracy = _repeatHistory
          .map((session) => session.accuracy)
          .reduce((a, b) => a + b) / _repeatHistory.length;
    }
  }

  Future<void> _initializeSpeech() async {
    print('🎤 Initializing speech recognition...');
    print('🎤 Platform: ${kIsWeb ? "Web" : "Mobile"}');

    try {
      // Cancel any existing session first
      if (_speechToText.isListening) {
        print('🎤 Stopping existing session...');
        await _speechToText.cancel();
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // Initialize with simple error handling
      final isAvailable = await _speechToText.initialize(
        onError: (error) async {
          if (!mounted) return;
          print('🎤 Speech error: ${error.errorMsg}');

          // Only show error for non-transient issues
          if (error.errorMsg != 'error_no_match') {
            String errorMessage = 'Microphone error';
            if (error.errorMsg.contains('permission')) {
              errorMessage =
                  'Microphone permission denied. Please allow access in Settings.';
            } else {
              errorMessage =
                  'Microphone error. Please check Settings > EchoVerse > Microphone.';
            }

            setState(() {
              _speechError = errorMessage;
              _isListening = false;
            });

            // Auto-retry once on common transient error
            if (error.errorMsg.contains('no-speech') &&
                _noSpeechRetryCount < 1) {
              _noSpeechRetryCount += 1;
              print(
                  '🎤 Auto-retrying listen after no-speech (retry #$_noSpeechRetryCount)');
              await Future.delayed(const Duration(milliseconds: 500));
              if (mounted) {
                await _startRepeatListening();
              }
            }
          }
        },
        onStatus: (status) {
          print('🎤 Speech status: $status');
          if (mounted && status == 'listening') {
            setState(() {
              _speechError = null;
            });
          }
          // Auto-restart if it stops unexpectedly in Repeat mode
          if (mounted && status == 'done' && _selectedMode == PracticeMode.repeat && _isListening) {
            print('🎤 Speech recognition stopped unexpectedly in Repeat mode, restarting...');
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted && _selectedMode == PracticeMode.repeat && _isListening) {
                _startRepeatListening();
              }
            });
          }
        },
      );

      print('🎤 Speech recognition available: $isAvailable');
      final hasPermission = await _speechToText.hasPermission;
      print('🎤 Has permission: $hasPermission');

      if (!mounted) return;
      setState(() {
        _speechAvailable = isAvailable;
        if (isAvailable) {
          _speechError = null;
        } else {
          _speechError =
              'Microphone not available. Please grant permission in Settings > EchoVerse.';
        }
      });
    } catch (error) {
      print('🎤 Speech init exception: $error');
      if (!mounted) return;
      setState(() {
        _speechAvailable = false;
        _speechError =
            'Failed to initialize microphone. Error: ${error.toString()}';
      });
    }
  }

  Future<void> _cancelRepeatCountdown() async {
    _repeatCountdownTimer?.cancel();
    _repeatCountdownTimer = null;
    if (_speechToText.isListening) {
      await _speechToText.stop();
    }
    if (!mounted) return;
    setState(() {
      _repeatCountdown = null;
    });
  }

  Future<void> _toggleRepeatListening() async {
    if (_isListening) {
      await _stopRepeatListening();
    } else {
      await _ensureRepeatListening();
    }
  }

  double _currentSoundLevel = 0.0;

  Future<void> _startRepeatListening() async {
    print('🎤 === STARTING SIMPLIFIED REPEAT MODE ===');
    _autoRestartTimer?.cancel();

    // Initialize if needed
    if (!_speechAvailable) {
      await _initializeSpeech();
    }

    if (!_speechAvailable) {
      if (!mounted) return;
      setState(() {
        _speechError = 'Microphone not available. Please check permissions.';
      });
      return;
    }

    // Stop any existing session
    if (_speechToText.isListening) {
      print('🎤 Stopping existing session...');
      await _speechToText.stop();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // Reset state
    if (!mounted) return;
    setState(() {
      _repeatMatchedWords = 0;
      _repeatAccuracy = 0.0;
      _repeatTranscript = '';
      _repeatMatchedWordIndices = <int>{};
      _speechError = null;
      _isListening = true;
      _repeatStart = DateTime.now();
      _currentSoundLevel = 0.0;
    });

    // Start listening with simple callback
    try {
      print('🎤 Starting to listen...');
      await _speechToText.listen(
        onResult: (result) {
          if (!mounted || !_isListening) return;
          print('🎤 Got result: "${result.recognizedWords}"');
          
          setState(() {
            _repeatTranscript = result.recognizedWords;
            
            // Calculate accuracy
            final evaluation = _evaluateRepeatProgress(result.recognizedWords);
            _repeatMatchedWords = evaluation.matchedCount;
            _repeatAccuracy = _normalizedWords.isEmpty ? 0.0 : evaluation.matchedCount / _normalizedWords.length;
            _repeatMatchedWordIndices = evaluation.matchedIndices;
          });
          
          // Auto-complete if accuracy is high enough (75%+)
          // Check this outside setState to avoid calling async function during build
          if (_repeatAccuracy >= 0.75 && _isListening) {
            print('🎤 ✅ Verse completed with ${(_repeatAccuracy * 100).toInt()}% accuracy!');
            HapticFeedback.mediumImpact();
            _completeRepeatMode();
          }
        },
        onSoundLevelChange: (level) {
          if (!mounted || !_isListening) return;
          setState(() {
            _currentSoundLevel = level.clamp(0.0, 1.0);
          });
        },
        listenOptions: SpeechListenOptions(
          listenMode: ListenMode.dictation,
          partialResults: true,
          cancelOnError: false,
        ),
        listenFor: const Duration(minutes: 10),
        pauseFor: const Duration(seconds: 30),
      );
      
      print('🎤 ✅ Listening started successfully');
    } catch (e) {
      print('🎤 ❌ Error starting: $e');
      if (!mounted) return;
      setState(() {
        _speechError = 'Failed to start: $e';
        _isListening = false;
      });
    }
  }

  Future<void> _completeRepeatMode() async {
    // Play success sound
    HapticFeedback.heavyImpact();
    
    // Stop listening
    await _stopRepeatListening(saveResult: true);
    
    // Show success message
    if (mounted) {
      _showSnackBar('🎉 Great job! You completed the verse!');
    }
  }

  // Removed - simplified into _startRepeatListening
  /*Future<void> _beginRepeatListening() async {
    // Final check - if speech is not available, don't proceed
    if (!_speechAvailable || !_speechToText.isAvailable) {
      if (!mounted) return;
      setState(() {
        _speechError =
            'Speech recognition not ready. Please grant microphone permission.';
        _repeatFeedback =
            'Microphone access required. Check Settings > EchoVerse > Microphone.';
      });
      _showSnackBar('Please grant microphone permission to use Repeat mode.');
      return;
    }

    // Start listening
    try {
      print('🎤 Starting to listen...');
      print('🎤 Speech available: $_speechAvailable');
      print('🎤 SpeechToText available: ${_speechToText.isAvailable}');
      print('🎤 Already listening: ${_speechToText.isListening}');

      // Check permission explicitly
      final hasPermission = await _speechToText.hasPermission;
      print('🎤 Has permission: $hasPermission');
      if (!hasPermission) {
        print('🎤 ❌ No permission! Need to request in Settings.');
        if (!mounted) return;
        setState(() {
          _speechError =
              'Microphone permission denied. Please allow access in Settings > EchoVerse > Microphone.';
          _repeatFeedback = 'Permission required to use microphone.';
        });
        _showSnackBar(
            'Please grant microphone permission in Settings > EchoVerse');
        return;
      }

      // Reset transient retry counter for this session
      _noSpeechRetryCount = 0;

      // Start listening with explicit locale
      print('🎤 About to call listen() with callback...');
      print('🎤 Current isListening: ${_speechToText.isListening}');
      print('🎤 Current isAvailable: ${_speechToText.isAvailable}');

      try {
        print('🎤 Calling _speechToText.listen()...');
        await _speechToText.listen(
        onResult: (result) {
          print('🎤 📢 REPEAT MODE onResult callback triggered! Words: "${result.recognizedWords}"');
          if (!mounted) return;
          try {
            _onSpeechResult(result);
          } catch (e) {
            print('🎤 Error in onResult callback: $e');
          }
        },
        onSoundLevelChange: (level) {
          // Update waveform animation based on sound level
          if (level.isFinite && mounted) {
            // Visual feedback only, no logging
          }
        },
          onStatus: (status) {
            print('🎤 REPEAT MODE status: $status');
            if (status == 'done' && mounted && _isListening) {
              print('🎤 ⚠️ Speech recognition stopped in Repeat mode!');
            }
          },
          listenOptions: SpeechListenOptions(
            listenMode: ListenMode.dictation,
            partialResults: true,
            cancelOnError: false,
          ),
          listenFor: const Duration(minutes: 5),
          pauseFor: const Duration(seconds: 10), // Increased to prevent premature stopping
          localeId: 'en_US',
        );
        print('🎤 listen() call completed successfully');
      } catch (e) {
        print('🎤 ❌ Error calling listen(): $e');
        if (!mounted) return;
        setState(() {
          _speechError = 'Failed to start listening: $e';
          _isListening = false;
        });
        return;
      }

      // Brief delay to allow mic to initialize
      await Future.delayed(const Duration(milliseconds: 200));

      if (!mounted) return;

      if (!_speechToText.isListening) {
        setState(() {
          _speechError =
              'Failed to start listening. ${_speechToText.lastError ?? "Please try again."}';
          _repeatFeedback =
              'Couldn\'t start microphone. Tap Start Reciting to retry.';
        });
        _showSnackBar('Unable to start listening. Please try again.');
        return;
      }

      // Successfully started listening - add haptic feedback
      HapticFeedback.lightImpact();
      
      final startedAt = DateTime.now();
      print('🎤 ✅ Successfully started listening in Repeat mode!');
      print('🎤 isListening: ${_speechToText.isListening}');
      setState(() {
        _isListening = true;
        _repeatStart = startedAt;
        _repeatElapsed = Duration.zero;
        _repeatFeedback = 'Listening! Speak the verse clearly.';
        _speechError = null;
      });

      // Start the timer to track elapsed time
      _repeatTimer?.cancel();
      _repeatTimer = Timer.periodic(const Duration(milliseconds: 200), (_) {
        if (!mounted || _repeatStart == null) return;
        final elapsed = DateTime.now().difference(_repeatStart!);
        if (!mounted) return;
        setState(() {
          _repeatElapsed = elapsed;
          if (_repeatMatchedWords > 0 && elapsed.inMilliseconds > 0) {
            final minutes = elapsed.inMilliseconds / 60000.0;
            _repeatWordsPerMinute =
                minutes <= 0 ? 0.0 : _repeatMatchedWords / minutes;
          }
        });
      });
    } catch (e) {
      print('🎤 Error starting speech recognition: $e');
      if (!mounted) return;
      setState(() {
        _speechError = 'Error: ${e.toString()}';
        _repeatFeedback =
            'Failed to start listening. Check microphone permissions.';
        _isListening = false;
      });
      _showSnackBar('Error starting microphone. Please check permissions.');
    }
  }*/

  Future<void> _stopRepeatListening(
      {bool saveResult = true, bool autoRestart = false}) async {
    print('🎤 Stopping Repeat mode...');
    if (_stopInProgress) return;
    _stopInProgress = true;
    
    try {
      if (_speechToText.isListening) {
        await _speechToText.stop();
      }
      final elapsed = _repeatStart == null
          ? Duration.zero
          : DateTime.now().difference(_repeatStart!);
      final elapsedMs = elapsed.inMilliseconds;
      final evaluation = _evaluateRepeatProgress(_repeatTranscript);
      final matched = evaluation.matchedCount;
      final accuracy =
          _normalizedWords.isEmpty ? 0.0 : matched / _normalizedWords.length;
      final wordsPerMinute = (elapsedMs > 0 && matched > 0)
          ? matched / (elapsedMs / 60000.0)
          : 0.0;
      final session = saveResult
          ? _RepeatSession(
              timestamp: DateTime.now(),
              matchedWords: matched,
              totalWords: _normalizedWords.length,
              accuracy: accuracy,
              wordsPerMinute: wordsPerMinute,
              elapsed: elapsed,
              focusWordsSnapshot: evaluation.focusWords,
              transcript: _repeatTranscript,
            )
          : null;
      if (mounted) {
        setState(() {
          _isListening = false;
          _repeatElapsed = elapsed;
          _repeatStart = null;
          _repeatWordsPerMinute = wordsPerMinute;
          _repeatMatchedWords = matched;
          _repeatAccuracy = accuracy;
          _repeatMatchedWordIndices = evaluation.matchedIndices;
          _repeatFocusWords = evaluation.focusWords;
          _repeatLongestStreak = evaluation.longestStreak;
          final feedback = _buildRepeatFeedbackMessage(
            matched,
            accuracy,
            wordsPerMinute,
            finished: true,
          );
          _repeatFeedback = feedback;
          if (saveResult && session != null) {
            _repeatHistory = [session, ..._repeatHistory].take(5).toList();
            _repeatBestAccuracy = max(_repeatBestAccuracy, accuracy);
            final totalAccuracy = _repeatHistory.fold<double>(
                0.0, (sum, item) => sum + item.accuracy);
            _repeatAverageAccuracy = _repeatHistory.isEmpty
                ? 0.0
                : totalAccuracy / _repeatHistory.length;
          }
        });
      }

      if (!saveResult) return;

      final bool metThreshold = accuracy >= _repeatAccuracyThreshold;
      if (metThreshold) {
        await _registerPractice();
        _showSnackBar('Great job! Repeat mode counted as practice.');
      } else if (!autoRestart) {
        _showSnackBar(
            'Aim for ${(100 * _repeatAccuracyThreshold).toStringAsFixed(0)}% accuracy to count as practice.');
      }

      if (autoRestart && !metThreshold) {
        _scheduleRepeatAutoRestart();
      }
    } finally {
      _stopInProgress = false;
    }
  }

  void _onSpeechResult(SpeechRecognitionResult result) {
    final recognized = result.recognizedWords;
    final isFinal = result.finalResult;
    
    // Log what the user is saying
    print('🎤 User said: "$recognized" (final: $isFinal)');

    final evaluation = _evaluateRepeatProgress(recognized);
    final matched = evaluation.matchedCount;
    final accuracy =
        _normalizedWords.isEmpty ? 0.0 : matched / _normalizedWords.length;

    final elapsed = _repeatStart == null
        ? Duration.zero
        : DateTime.now().difference(_repeatStart!);
    final elapsedMs = elapsed.inMilliseconds;
    final wordsPerMinute =
        (elapsedMs > 0 && matched > 0) ? matched / (elapsedMs / 60000.0) : 0.0;
    final feedback =
        _buildRepeatFeedbackMessage(matched, accuracy, wordsPerMinute);
    if (!mounted) return;

    setState(() {
      _repeatMatchedWords = matched;
      _repeatAccuracy = accuracy;
      _repeatTranscript = recognized;
      _repeatElapsed = elapsed;
      _repeatWordsPerMinute = wordsPerMinute;
      _repeatFeedback = feedback;
      _repeatMatchedWordIndices = evaluation.matchedIndices;
      _repeatFocusWords = evaluation.focusWords;
      _repeatLongestStreak = evaluation.longestStreak;

      // Mark words that weren't matched as missed
      _repeatIncorrectWordIndices = {
        for (int i = 0; i < _normalizedWords.length; i++)
          if (!_repeatMatchedWordIndices.contains(i)) i
      };

      // Check if verse is completed (high accuracy)
      if (!_isListening &&
          _repeatAccuracy >= 0.85 &&
          _repeatMatchedWords >= (_normalizedWords.length * 0.85).round()) {
        _isRepeatCompleted = true;
      }
    });

    if (isFinal && !_stopInProgress) {
      final bool shouldAutoRestart =
          accuracy < _repeatAccuracyThreshold && !kIsWeb;
      unawaited(_stopRepeatListening(autoRestart: shouldAutoRestart));
    }
  }

  _RepeatEvaluation _evaluateRepeatProgress(String text) {
    if (text.trim().isEmpty) {
      return const _RepeatEvaluation.empty();
    }

    final recognizedTokens = text
        .split(RegExp(r'\s+'))
        .map(_normalizeWord)
        .where((token) => token.isNotEmpty)
        .toList();

    if (recognizedTokens.isEmpty) {
      return const _RepeatEvaluation.empty();
    }

    final matchedIndices = <int>{};
    final focusCandidates = <String>[];
    int tokenIndex = 0;

    for (int wordIndex = 0; wordIndex < _normalizedWords.length; wordIndex++) {
      final expectedWord = _normalizedWords[wordIndex];

      bool foundMatch = false;
      for (int lookAhead = 0;
          lookAhead < min(3, recognizedTokens.length - tokenIndex);
          lookAhead++) {
        final candidateIndex = tokenIndex + lookAhead;
        if (candidateIndex >= recognizedTokens.length) break;
        final token = recognizedTokens[candidateIndex];

        final matches = _wordsRoughlyMatch(token, expectedWord);

        if (matches) {
          matchedIndices.add(wordIndex);
          tokenIndex = candidateIndex + 1;
          foundMatch = true;
          break;
        }
      }

      if (!foundMatch) {
        focusCandidates.add(_words[wordIndex]);
        if (tokenIndex < recognizedTokens.length) {
          bool canSkip = false;
          for (int futureWord = wordIndex + 1;
              futureWord < min(wordIndex + 3, _normalizedWords.length);
              futureWord++) {
            if (_wordsRoughlyMatch(
                recognizedTokens[tokenIndex], _normalizedWords[futureWord])) {
              canSkip = true;
              break;
            }
          }
          if (!canSkip) {
            tokenIndex++;
          }
        }
      }
    }

    final longestStreak = _calculateLongestStreak(matchedIndices);

    return _RepeatEvaluation(
      matchedCount: matchedIndices.length,
      matchedIndices: matchedIndices,
      focusWords: focusCandidates.take(4).toList(),
      longestStreak: longestStreak,
    );
  }

  int _calculateLongestStreak(Set<int> matchedIndices) {
    if (matchedIndices.isEmpty) return 0;
    final sorted = matchedIndices.toList()..sort();
    int longest = 1;
    int current = 1;
    for (int i = 1; i < sorted.length; i++) {
      if (sorted[i] == sorted[i - 1] + 1) {
        current++;
        longest = max(longest, current);
      } else {
        current = 1;
      }
    }
    return longest;
  }

  bool _wordsRoughlyMatch(String spoken, String expected) {
    if (spoken.isEmpty || expected.isEmpty) return false;

    // Exact match
    if (spoken == expected) return true;

    // Check for common contractions and variations
    final spokenVariants = _getWordVariants(spoken);
    final expectedVariants = _getWordVariants(expected);
    for (final sv in spokenVariants) {
      for (final ev in expectedVariants) {
        if (sv == ev) return true;
      }
    }

    // More lenient substring matches for pronunciation issues
    // If the expected word is contained in the spoken word (handles concatenation)
    if (spoken.contains(expected) && expected.length >= 3) return true;
    if (expected.contains(spoken) && spoken.length >= 3) return true;
    
    // Check if spoken word starts with expected (handles partial matches)
    if (spoken.startsWith(expected) && expected.length >= 3) return true;
    if (expected.startsWith(spoken) && spoken.length >= 3) return true;
    
    // Original substring check
    if (spoken.contains(expected) &&
        (spoken.length - expected.length).abs() <= 3) return true;
    if (expected.contains(spoken) &&
        (spoken.length - expected.length).abs() <= 3) return true;

    // First character must match for short words
    if (spoken.isNotEmpty && expected.isNotEmpty) {
      if (spoken.codeUnitAt(0) != expected.codeUnitAt(0)) {
        // Allow for common sound-alikes (e.g., "the" vs "thee")
        if (spoken.length > 2 && expected.length > 2) {
          // Check if first two characters match (more forgiving)
          if (spoken.length >= 2 && expected.length >= 2) {
            if (spoken.substring(0, min(2, spoken.length)) !=
                expected.substring(0, min(2, expected.length))) {
              return false;
            }
          }
        } else {
          return false;
        }
      }
    }

    // For very short words, require exact match
    if (spoken.length <= 2 || expected.length <= 2) {
      return spoken == expected;
    }

    // Calculate Levenshtein distance for fuzzy matching
    final distance = _levenshteinDistance(spoken, expected);
    final maxLength = max(spoken.length, expected.length);

    // More forgiving matching: allow up to 30% of word length difference
    final allowance = max(1, (maxLength * 0.3).ceil());
    return distance <= allowance;
  }

  List<String> _getWordVariants(String word) {
    final variants = <String>[word.toLowerCase()];

    // Handle common contractions
    if (word.contains("'")) {
      variants.add(word.replaceAll("'", ""));
    }
    if (!word.contains("'") && word.endsWith('s')) {
      variants.add('${word.substring(0, word.length - 1)}\'s');
    }

    // Handle common word endings
    if (word.endsWith('ed')) {
      variants.add(word.substring(0, word.length - 1)); // "loved" -> "love"
    }
    if (word.endsWith('ing')) {
      variants.add(word.substring(0, word.length - 3)); // "loving" -> "love"
    }

    return variants;
  }

  int _levenshteinDistance(String a, String b) {
    final m = a.length;
    final n = b.length;
    final dp = List.generate(m + 1, (_) => List<int>.filled(n + 1, 0));
    for (int i = 0; i <= m; i++) {
      dp[i][0] = i;
    }
    for (int j = 0; j <= n; j++) {
      dp[0][j] = j;
    }
    for (int i = 1; i <= m; i++) {
      for (int j = 1; j <= n; j++) {
        final cost = a.codeUnitAt(i - 1) == b.codeUnitAt(j - 1) ? 0 : 1;
        dp[i][j] = min(
          min(dp[i - 1][j] + 1, dp[i][j - 1] + 1),
          dp[i - 1][j - 1] + cost,
        );
      }
    }
    return dp[m][n];
  }

  String _buildRepeatFeedbackMessage(
      int matched, double accuracy, double wordsPerMinute,
      {bool finished = false}) {
    final accuracyPercent = (accuracy * 100).round();
    if (matched == 0) {
      return finished
          ? 'Let\'s try again—start by echoing the opening phrase clearly.'
          : 'When the countdown ends, open strong with the first phrase to lock in the rhythm.';
    }

    final targetPercent = (100 * _repeatAccuracyThreshold).round();
    final tempo = _tempoDescriptor(wordsPerMinute);

    if (finished) {
      if (accuracy >= _repeatAccuracyThreshold) {
        return 'You reached $accuracyPercent% accuracy at a $tempo—practice recorded! Keep refining that flow.';
      }
      return 'You wrapped at $accuracyPercent% accuracy. Review the tricky words and aim for $targetPercent% next run.';
    }

    if (accuracy >= 0.85) {
      return 'Beautiful recall ($accuracyPercent%)—stay in that $tempo as you move to the next phrase.';
    }
    if (accuracy >= 0.6) {
      return 'Good momentum. Focus on crisp consonants to raise accuracy above 85%.';
    }
    return 'Slow your pace and articulate each word. Let the verse lead and match each phrase before moving on.';
  }

  String _confidenceLabel(double accuracy) {
    if (accuracy >= 0.85) return 'High';
    if (accuracy >= 0.6) return 'Building';
    if (accuracy > 0) return 'Warming up';
    return 'Not started';
  }

  Color _confidenceColor(ThemeData theme, double accuracy) {
    if (accuracy >= 0.85) return theme.colorScheme.secondary;
    if (accuracy >= 0.6) return theme.colorScheme.primary;
    if (accuracy > 0) return theme.colorScheme.tertiary;
    return theme.colorScheme.onSurface.withValues(alpha: 0.3);
  }

  String _nextRepeatSegment({int length = 6}) {
    if (_repeatMatchedWordIndices.length >= _words.length) {
      return 'All words matched—finish the verse with confidence.';
    }
    int startIndex = 0;
    while (startIndex < _words.length &&
        _repeatMatchedWordIndices.contains(startIndex)) {
      startIndex++;
    }
    final remaining = _words.length - startIndex;
    if (remaining <= 0) {
      return 'Keep the cadence going.';
    }
    final take = min(length, max(1, remaining));
    final segment = _words.skip(startIndex).take(take).join(' ');
    return segment.isEmpty ? 'Keep the cadence going.' : segment;
  }

  String _tempoDescriptor(double wordsPerMinute) {
    if (wordsPerMinute <= 0) return 'finding your rhythm';
    if (wordsPerMinute < 70) return 'gentle pace';
    if (wordsPerMinute < 110) return 'steady pace';
    if (wordsPerMinute < 140) return 'energetic pace';
    return 'high-energy pace';
  }

  Future<void> _registerPractice() async {
    setState(() {
      _practiceCount++;
    });
    final fadeLevel = _calculateFadeLevel();
    final masteryLevel = ((1.0 - fadeLevel) * 100).clamp(0, 100).toInt();
    await widget.progressService
        .incrementPractice(widget.userId, widget.verse.id, masteryLevel);
  }

  double _calculateFadeLevel() {
    if (_practiceCount == 0) return 0.0;
    if (_practiceCount >= 10) return 0.8;
    return (_practiceCount / 10) * 0.8;
  }

  bool _wordShouldHighlight(int index) {
    return switch (_selectedMode) {
      PracticeMode.listen => index == _currentWordIndex,
      PracticeMode.repeat => _repeatMatchedWordIndices.contains(index),
      PracticeMode.sayAfter => false, // Handled separately in _buildSayAfterVerse
      PracticeMode.music => false,
    };
  }


  void _showSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  String _modeLabel(PracticeMode mode) {
    return switch (mode) {
      PracticeMode.listen => 'Listen',
      PracticeMode.repeat => 'Repeat',
      PracticeMode.sayAfter => 'Say After',
      PracticeMode.music => 'Music',
    };
  }

  String _modeDescription(PracticeMode mode) {
    return switch (mode) {
      PracticeMode.listen =>
        'Hear the verse narrated while the words highlight in sync.',
      PracticeMode.repeat =>
        'Recite with confidence—speech recognition scores your timing and accuracy.',
      PracticeMode.sayAfter =>
        'Listen to small chunks and repeat after each one. Perfect for memorization!',
      PracticeMode.music =>
        'Generate beautiful instrumental music inspired by this verse.',
    };
  }

  String _normalizeWord(String word) {
    return word.toLowerCase().replaceAll(RegExp(r"[^a-z0-9']"), '');
  }

  String _relativeTimeLabel(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    if (difference.isNegative) {
      return 'just now';
    }
    if (difference.inMinutes < 1) {
      return 'just now';
    }
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    }
    if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    }
    if (difference.inDays == 1) {
      return 'yesterday';
    }
    if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    }
    return '${timestamp.month}/${timestamp.day}/${timestamp.year}';
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    final minuteText = minutes.toString().padLeft(1, '0');
    final secondText = seconds.toString().padLeft(2, '0');
    return '$minuteText:$secondText';
  }

  PreferredSizeWidget _buildEnhancedAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        widget.verse.reference,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.more_vert, color: theme.colorScheme.onSurface),
          onPressed: () => _showOptionsMenu(context),
        ),
      ],
    );
  }

  Widget _buildEnhancedModeSelector(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 12, 24, 0),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: PracticeMode.values.map((mode) {
          final isSelected = _selectedMode == mode;
          return Expanded(
            child: GestureDetector(
              onTap: () => _changeMode(mode),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getModeIcon(mode),
                      color: isSelected
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _modeLabel(mode),
                      textAlign: TextAlign.center,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: isSelected
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEnhancedVerseContainer(ThemeData theme, double fadeLevel) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.brightness == Brightness.light
            ? Colors.white
            : theme.colorScheme.primaryContainer.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: switch (_selectedMode) {
        PracticeMode.repeat => _buildRepeatVerse(theme),
        PracticeMode.sayAfter => _buildSayAfterVerse(theme),
        PracticeMode.music => _buildMusicMode(theme),
        _ => Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _words.asMap().entries.map((entry) {
              final index = entry.key;
              final word = entry.value;
              return KaraokeWord(
                word: word,
                isHighlighted: _wordShouldHighlight(index),
                fadeLevel: fadeLevel,
              );
            }).toList(),
          ),
      },
    );
  }

  Widget _buildSayAfterVerse(ThemeData theme) {
    if (_sayAfterChunks.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Status indicator
        if (_isSayAfterPlaying)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.volume_up, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Text(
                  '🔊 Listen carefully...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          )
        else if (_isSayAfterListening)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.mic, color: Colors.green, size: 20),
                const SizedBox(width: 8),
                Text(
                  '🎤 Now repeat it!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        
        // Verse chunks
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _sayAfterChunks.asMap().entries.expand((chunkEntry) {
            final chunkIndex = chunkEntry.key;
            final chunk = chunkEntry.value;
            final isCurrentChunk = chunkIndex == _currentChunkIndex;
            
            return chunk.words.map((word) {
              return _buildChunkWord(
                word,
                chunk.state,
                isCurrentChunk,
                theme,
                onTap: () => _playChunkByIndex(chunkIndex, restart: true),
              );
            }).toList();
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildChunkWord(String word, ChunkState state, bool isCurrentChunk, ThemeData theme, {VoidCallback? onTap}) {
    Color backgroundColor;
    Color textColor;
    
    switch (state) {
      case ChunkState.pending:
        backgroundColor = theme.colorScheme.surface;
        textColor = theme.colorScheme.onSurface.withValues(alpha: 0.4);
        break;
      case ChunkState.playing:
        backgroundColor = theme.colorScheme.primary.withValues(alpha: 0.2);
        textColor = theme.colorScheme.primary;
        break;
      case ChunkState.listening:
        backgroundColor = Colors.orange.withValues(alpha: 0.2);
        textColor = Colors.orange.shade700;
        break;
      case ChunkState.correct:
        backgroundColor = Colors.green.withValues(alpha: 0.2);
        textColor = Colors.green.shade700;
        break;
      case ChunkState.incorrect:
        backgroundColor = Colors.red.withValues(alpha: 0.2);
        textColor = Colors.red.shade700;
        break;
    }

    final chip = AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: isCurrentChunk ? Border.all(
          color: theme.colorScheme.primary,
          width: 2,
        ) : null,
      ),
      child: Text(
        word,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: textColor,
          fontWeight: isCurrentChunk ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );

    if (onTap == null) return chip;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: chip,
    );
  }

  Widget _buildEnhancedBottomControls(ThemeData theme, double fadeLevel) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
          ),
        ),
      ),
      child: _buildModeControls(theme),
    );
  }

  IconData _getModeIcon(PracticeMode mode) {
    switch (mode) {
      case PracticeMode.listen:
        return Icons.headphones;
      case PracticeMode.repeat:
        return Icons.mic;
      case PracticeMode.sayAfter:
        return Icons.record_voice_over;
      case PracticeMode.music:
        return Icons.music_note;
    }
  }



  // Say After Mode Methods
  void _initializeSayAfterMode() {
    _createChunks(_sayAfterChunkSize);
    _currentChunkIndex = 0;
    if (!kIsWeb && !_speechAvailable) {
      unawaited(_initializeSpeech());
    }
  }

  void _createChunks(int chunkSize) {
    _sayAfterChunks.clear();
    final words = _words;

    // Create chunks that prioritize natural speech patterns and semantic meaning
    int i = 0;
    while (i < words.length) {
      int j = i;
      final int minChunkSize = max(2, (chunkSize * 0.5).round()); // Minimum 2 words
      final int maxChunkSize = chunkSize + 3; // Allow some flexibility

      // First, try to find a natural break point within our target range
      int bestBreakPoint = -1;
      int bestScore = -1;

      while (j < words.length && (j - i) < maxChunkSize) {
        j++;
        final currentLength = j - i;

        // Skip if we haven't reached minimum chunk size
        if (currentLength < minChunkSize) continue;

        // Calculate score for this potential break point
        int score = _calculateBreakScore(words, j - 1, currentLength, chunkSize);

        if (score > bestScore) {
          bestScore = score;
          bestBreakPoint = j;
        }

        // If we found a perfect break (sentence end), use it immediately
        if (score >= 100) break;
      }

      // Use the best break point we found, or fall back to target size
      final endIndex = bestBreakPoint != -1 ? bestBreakPoint : min(i + chunkSize, words.length);
      final chunkWords = words.sublist(i, endIndex);

      _sayAfterChunks.add(VerseChunk(
        words: chunkWords,
        startIndex: i,
        endIndex: endIndex,
      ));
      i = endIndex;
    }

    if (mounted) setState(() {});
  }

  // Calculate a score for how good a break point is (higher = better)
  int _calculateBreakScore(List<String> words, int wordIndex, int chunkLength, int targetSize) {
    if (wordIndex >= words.length) return 0;

    final word = words[wordIndex];
    int score = 0;

    // Strongly prefer sentence endings
    if (word.endsWith('.') || word.endsWith('!') || word.endsWith('?')) {
      score += 100;
    }
    // Moderately prefer clause endings
    else if (word.endsWith(',') || word.endsWith(';') || word.endsWith(':')) {
      score += 60;
    }

    // Prefer chunks close to target size
    final sizeDiff = (chunkLength - targetSize).abs();
    if (sizeDiff == 0) {
      score += 40;
    } else if (sizeDiff == 1) {
      score += 30;
    } else if (sizeDiff == 2) {
      score += 20;
    } else if (sizeDiff <= 3) {
      score += 10;
    }

    // Bonus for common phrase patterns
    if (_isCommonPhraseEnd(words, wordIndex)) {
      score += 25;
    }

    // Penalty for breaking in the middle of common phrases
    if (_isInMiddleOfPhrase(words, wordIndex)) {
      score -= 30;
    }

    return score;
  }

  bool _isCommonPhraseEnd(List<String> words, int index) {
    if (index >= words.length) return false;

    final word = words[index].toLowerCase();
    final prevWord = index > 0 ? words[index - 1].toLowerCase() : '';

    // Common phrase endings
    final phraseEndings = {
      'lord', 'god', 'christ', 'jesus', 'spirit', 'father', 'heaven', 'earth',
      'amen', 'forever', 'evermore', 'always', 'never', 'all', 'every', 'everyone'
    };

    // Preposition + noun combinations that often end phrases
    final prepositions = {'in', 'on', 'of', 'for', 'with', 'by', 'through', 'unto'};

    return phraseEndings.contains(word) ||
           (prepositions.contains(prevWord) && phraseEndings.contains(word));
  }

  bool _isInMiddleOfPhrase(List<String> words, int index) {
    if (index >= words.length - 1) return false;

    final word = words[index].toLowerCase();
    final nextWord = words[index + 1].toLowerCase();

    // Don't break after articles, prepositions, or conjunctions
    final dontBreakAfter = {
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
      'before', 'after', 'above', 'below', 'between', 'among', 'that', 'which',
      'who', 'whom', 'whose', 'when', 'where', 'why', 'how', 'if', 'unless',
      'until', 'while', 'since', 'because', 'although', 'though', 'whereas'
    };

    // Don't break before common word endings
    final dontBreakBefore = {
      'not', 'never', 'always', 'forever', 'evermore', 'indeed', 'truly',
      'surely', 'certainly', 'therefore', 'however', 'moreover', 'furthermore'
    };

    return dontBreakAfter.contains(word) || dontBreakBefore.contains(nextWord);
  }

  Future<void> _playCurrentChunk() async {
    if (_currentChunkIndex >= _sayAfterChunks.length) return;

    final chunk = _sayAfterChunks[_currentChunkIndex];

    // Comprehensive audio session cleanup to prevent echo
    await _stopAllAudioAndListening();

    setState(() {
      chunk.state = ChunkState.playing;
      _isSayAfterPlaying = true;
      _isSayAfterListening = false;
    });

    _audioService.setSpeechRate(widget.settingsService.audioSpeed);
    _audioService.setVoiceId(widget.settingsService.voiceType.id);

    print('🎤 🔊 Playing chunk: "${chunk.text}"');

    await _audioService.speakVerse(
      chunk.text,
      onWord: (_) {},
      onPlayingStateChanged: (playing) {
        if (!playing && mounted && _isSayAfterPlaying) {
          print('🎤 ✅ Playback finished, implementing echo prevention...');
          setState(() => _isSayAfterPlaying = false);

          // Cancel any existing timer to prevent duplicates
          _sayAfterTimer?.cancel();

          // Enhanced delay calculation based on platform and audio characteristics
          final delayMs = _calculateOptimalDelay();
          _sayAfterTimer = Timer(Duration(milliseconds: delayMs), () {
            if (mounted && !_isSayAfterListening) {
              print('🎤 👂 Echo prevention complete, ready for speech!');
              _listenForChunk();
            }
          });
        }
      },
      onPositionChanged: (_) {},
      onDurationChanged: (_) {},
    );
  }

  // Calculate optimal delay to prevent echo based on platform and conditions
  int _calculateOptimalDelay() {
    // Base delays by platform (empirically determined)
    int baseDelay;
    if (kIsWeb) {
      baseDelay = 2000; // Web needs more time due to browser audio handling
    } else if (Platform.isIOS) {
      baseDelay = 2500; // iOS has aggressive echo cancellation but needs time
    } else {
      baseDelay = 3000; // Android varies widely, be conservative
    }

    // Add extra delay for slower speech rates (more audio to clear)
    final speechRate = widget.settingsService.audioSpeed;
    if (speechRate < 0.8) {
      baseDelay += 1000; // Slower speech = longer audio = more echo risk
    } else if (speechRate > 1.2) {
      baseDelay -= 500; // Faster speech = shorter audio = less echo risk
    }

    return baseDelay;
  }

  // Comprehensive cleanup to prevent audio interference
  Future<void> _stopAllAudioAndListening() async {
    // Stop speech recognition with proper cleanup
    if (_speechToText.isListening) {
      print('🎤 Stopping speech recognition...');
      await _speechToText.stop();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // Cancel any pending speech operations
    if (_speechToText.isAvailable) {
      await _speechToText.cancel();
      await Future.delayed(const Duration(milliseconds: 300));
    }

    // Ensure audio service is fully stopped
    await _audioService.stop();
    await Future.delayed(const Duration(milliseconds: 200));

    // Clear any residual audio buffers (platform-specific)
    if (!kIsWeb) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
  }

  Future<void> _listenForChunk() async {
    if (_currentChunkIndex >= _sayAfterChunks.length) return;
    
    // Prevent multiple simultaneous calls
    if (_isSayAfterListening) {
      print('🎤 Already listening, skipping duplicate call');
      return;
    }
    
    print('🎤 === LISTENING FOR CHUNK ${_currentChunkIndex + 1} ===');
    
    final chunk = _sayAfterChunks[_currentChunkIndex];
    
    if (!_speechAvailable) {
      await _initializeSpeech();
    }

    if (!_speechAvailable) {
      print('🎤 ❌ Speech not available');
      return;
    }

    // Comprehensive pre-listening cleanup
    await _prepareForListening();

    setState(() {
      chunk.state = ChunkState.listening;
      _isSayAfterListening = true;
      _sayAfterTranscript = '';
    });

    try {
      print('🎤 Starting to listen for chunk: "${chunk.text}"');
      await _speechToText.listen(
        onResult: (result) {
          if (!mounted || !_isSayAfterListening) return;
          
          final transcript = result.recognizedWords.toLowerCase();
          print('🎤 Chunk heard: "$transcript"');
          
          setState(() {
            _sayAfterTranscript = transcript;
          });
          
          // Auto-evaluate on partial results too (more responsive)
          _evaluateChunkResponse(transcript);
        },
        onSoundLevelChange: (level) {
          if (!mounted) return;
          setState(() {
            _currentSoundLevel = level.clamp(0.0, 1.0);
          });
        },
        listenOptions: SpeechListenOptions(
          listenMode: ListenMode.dictation,
          partialResults: true,
          cancelOnError: false,
        ),
        listenFor: const Duration(seconds: 25), // Longer timeout for user comfort
        pauseFor: const Duration(seconds: 8), // Shorter pause to be more responsive
        localeId: 'en_US',
      );
      
      print('🎤 ✅ Listening started for chunk');
      
      // Verify it's actually listening after a brief delay
      await Future.delayed(const Duration(milliseconds: 500));
      if (!_speechToText.isListening && mounted && _isSayAfterListening) {
        print('🎤 ⚠️ Speech recognition not actually listening, retrying...');
        setState(() {
          _isSayAfterListening = false;
        });
        // Retry once
        await Future.delayed(const Duration(milliseconds: 500));
        _listenForChunk();
      }
    } catch (e) {
      print('🎤 ❌ Error starting listen: $e');
      if (mounted) {
        setState(() {
          _isSayAfterListening = false;
          chunk.state = ChunkState.pending;
        });
        _showSnackBar('Microphone error. Please try again.');
      }
    }
  }

  // Prepare audio environment for clean listening
  Future<void> _prepareForListening() async {
    // Stop any existing listening session with proper cleanup
    if (_speechToText.isListening) {
      print('🎤 Stopping existing listening session...');
      await _speechToText.stop();
      await Future.delayed(const Duration(milliseconds: 600));
    }

    // Cancel any pending operations
    if (_speechToText.isAvailable) {
      await _speechToText.cancel();
      await Future.delayed(const Duration(milliseconds: 400));
    }

    // Platform-specific audio preparation
    if (!kIsWeb) {
      // Give extra time for mobile audio systems to settle
      await Future.delayed(const Duration(milliseconds: 500));
    }

    print('🎤 Audio environment prepared for listening');
  }

  void _evaluateChunkResponse(String transcript) {
    if (_currentChunkIndex >= _sayAfterChunks.length) return;
    if (!_isSayAfterListening) return; // Already evaluated

    final chunk = _sayAfterChunks[_currentChunkIndex];
    final expectedWords = chunk.words.map(_normalizeWord).toList();
    final spokenWords = transcript
        .split(RegExp(r'\s+'))
        .map(_normalizeWord)
        .where((w) => w.isNotEmpty)
        .toList();

    // Calculate accuracy using fuzzy matching
    final lcsLen = _fuzzyLcsLength(expectedWords, spokenWords);
    final accuracy = expectedWords.isEmpty ? 0.0 : lcsLen / expectedWords.length;
    
    print('🎤 Chunk accuracy: ${(accuracy * 100).toInt()}% (${lcsLen}/${expectedWords.length} words)');

    // Dynamic threshold based on chunk length for better user experience
    double threshold = expectedWords.length <= 3 ? 0.70 : // Short chunks need higher accuracy
                      expectedWords.length <= 5 ? 0.60 : // Medium chunks
                      0.55; // Longer chunks can be more forgiving

    if (accuracy >= threshold) {
      print('🎤 ✅ Chunk accepted!');
      HapticFeedback.lightImpact();
      
      setState(() {
        _isSayAfterListening = false;
        chunk.state = ChunkState.correct;
      });
      
      // Stop listening
      if (_speechToText.isListening) {
        _speechToText.stop();
      }
      
      // Move to next chunk after brief delay
      _sayAfterTimer?.cancel();
      _sayAfterTimer = Timer(const Duration(milliseconds: 600), () {
        if (mounted) _moveToNextChunk();
      });
    } else if (spokenWords.length >= expectedWords.length) {
      // They've spoken enough words but accuracy is low - retry
      print('🎤 ⚠️ Low accuracy, replaying chunk...');
      
      setState(() {
        _isSayAfterListening = false;
        chunk.state = ChunkState.incorrect;
      });
      
      // Stop listening
      if (_speechToText.isListening) {
        _speechToText.stop();
      }
      
      // Replay the chunk after a brief delay
      _sayAfterTimer?.cancel();
      _sayAfterTimer = Timer(const Duration(milliseconds: 800), () {
        if (mounted) {
          chunk.state = ChunkState.pending;
          _playCurrentChunk();
        }
      });
    }
    // Otherwise keep listening for more words
  }

  void _moveToNextChunk() {
    _currentChunkIndex++;
    
    if (_currentChunkIndex >= _sayAfterChunks.length) {
      // Completed all chunks!
      setState(() {
        _isSayAfterPlaying = false;
        _isSayAfterListening = false;
      });
      unawaited(_registerPractice());
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🎉 Great job! You completed the verse!'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      // Play next chunk
      unawaited(_playCurrentChunk());
    }
  }

  void _retryCurrentChunk() {
    if (_currentChunkIndex < _sayAfterChunks.length) {
      final chunk = _sayAfterChunks[_currentChunkIndex];
      chunk.state = ChunkState.pending;
      unawaited(_playCurrentChunk());
    }
  }

  void _startSayAfterMode() {
    _initializeSayAfterMode();
    unawaited(_playCurrentChunk());
  }

  Future<void> _stopSayAfterMode() async {
    print('🎤 Stopping Say After mode...');
    _sayAfterTimer?.cancel();
    await _audioService.stop();
    if (_speechToText.isListening) {
      print('🎤 Stopping speech recognition from Say After mode...');
      await _speechToText.stop();
      // Wait for it to fully stop
      await Future.delayed(const Duration(milliseconds: 300));
    }
    
    if (!mounted) return;
    setState(() {
      _isSayAfterPlaying = false;
      _isSayAfterListening = false;
      _currentChunkIndex = 0;
      for (final chunk in _sayAfterChunks) {
        chunk.state = ChunkState.pending;
        chunk.retryCount = 0;
      }
    });
  }

  void _pauseSayAfterMode() {
    _sayAfterTimer?.cancel();
    _audioService.pause();
    if (_speechToText.isListening) {
      _speechToText.stop();
    }
    
    setState(() {
      _isSayAfterPlaying = false;
      _isSayAfterListening = false;
    });
  }

  void _updateChunkSize(int newSize) {
    setState(() {
      _sayAfterChunkSize = newSize;
    });
    _stopSayAfterMode();
    _initializeSayAfterMode();
  }

  void _playChunkByIndex(int index, {bool restart = false}) {
    if (index < 0 || index >= _sayAfterChunks.length) return;
    _sayAfterTimer?.cancel();
    _audioService.stop();
    if (_speechToText.isListening) {
      _speechToText.stop();
    }
    setState(() {
      _currentChunkIndex = index;
      for (int i = 0; i < _sayAfterChunks.length; i++) {
        if (restart || _sayAfterChunks[i].state != ChunkState.correct) {
          _sayAfterChunks[i].state = i == index ? ChunkState.pending : _sayAfterChunks[i].state;
        }
      }
    });
    unawaited(_playCurrentChunk());
  }



  // Helpers for fuzzy matching

  bool _fuzzyEquals(String a, String b) {
    if (a == b) return true;
    // Small typos allowed: distance 1 for short words, 2 for longer
    final d = _levenshteinDistance(a, b);
    if (a.length <= 4 || b.length <= 4) {
      return d <= 1;
    }
    return d <= 2;
  }

  int _fuzzyLcsLength(List<String> a, List<String> b) {
    final m = a.length;
    final n = b.length;
    final dp = List.generate(m + 1, (_) => List<int>.filled(n + 1, 0));
    for (int i = 1; i <= m; i++) {
      for (int j = 1; j <= n; j++) {
        if (_fuzzyEquals(a[i - 1], b[j - 1])) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }
    return dp[m][n];
  }

  Widget _buildSayAfterControls(ThemeData theme) {
    final isActive = _isSayAfterPlaying || _isSayAfterListening;
    final completedChunks = _sayAfterChunks.where((c) => c.state == ChunkState.correct).length;
    final totalChunks = _sayAfterChunks.length;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Chunk size selector
        if (!isActive)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Chunk Size: $_sayAfterChunkSize words',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _sayAfterChunkSize == _words.length ? 'Full verse' : '$_sayAfterChunkSize/${_words.length}',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: theme.colorScheme.primary,
                    inactiveTrackColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                    thumbColor: theme.colorScheme.primary,
                    overlayColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                    trackHeight: 4,
                  ),
                 child: Slider(
                   value: _sayAfterChunkSize.toDouble(),
                   min: 1,
                   max: _words.length.toDouble(),
                   divisions: _words.length - 1,
                   label: _sayAfterChunkSize == _words.length 
                       ? 'Full verse' 
                       : '$_sayAfterChunkSize words',
                   onChanged: (value) => _updateChunkSize(value.round()),
                 ),
               ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Gap before mic: ${(_sayAfterGapMs / 1000).toStringAsFixed(1)}s',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    Text(
                      'Responsive',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: theme.colorScheme.secondary,
                    inactiveTrackColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                    thumbColor: theme.colorScheme.secondary,
                    trackHeight: 4,
                  ),
                  child: Slider(
                    value: _sayAfterGapMs.toDouble(),
                    min: 0,
                    max: 1500,
                    divisions: 15,
                    label: '${(_sayAfterGapMs / 1000).toStringAsFixed(1)}s',
                    onChanged: (value) => setState(() => _sayAfterGapMs = value.round()),
                  ),
                ),
              ],
            ),
          ),
        
        // Progress indicator
        if (isActive || completedChunks > 0)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.check_circle, 
                    color: Colors.green, size: 20),
                const SizedBox(width: 8),
                Text(
                  '$completedChunks / $totalChunks chunks completed',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        
        // Status indicator
        if (_isSayAfterPlaying)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.volume_up, color: theme.colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Listen carefully...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        
        if (_isSayAfterListening)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.mic, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Now you say it!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
          ),
        
        // Action buttons
        Row(
          children: [
            if (isActive) ...[
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _pauseSayAfterMode,
                  icon: const Icon(Icons.pause, size: 20),
                  label: const Text('Pause'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _playChunkByIndex(_currentChunkIndex, restart: true),
                  icon: const Icon(Icons.replay, size: 20),
                  label: const Text('Replay'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _stopSayAfterMode,
                  icon: const Icon(Icons.stop, size: 20),
                  label: const Text('Stop'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.error,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: completedChunks == totalChunks && totalChunks > 0
                      ? _stopSayAfterMode
                      : _startSayAfterMode,
                  icon: Icon(
                    completedChunks == totalChunks && totalChunks > 0
                        ? Icons.refresh
                        : Icons.play_arrow,
                    size: 20,
                  ),
                  label: Text(
                    completedChunks == totalChunks && totalChunks > 0
                        ? 'Start Over'
                        : 'Start Practice',
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              if (!isActive && completedChunks < totalChunks)
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _moveToNextChunk(),
                    icon: const Icon(Icons.skip_next, size: 20),
                    label: const Text('Skip'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                    ),
                  ),
                ),
            ],
          ],
        ),

      ],
    );
  }

  void _showOptionsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.bookmark_add),
              title: const Text('Add to Favorites'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Verse'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Practice Settings'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('Verse Info'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }
}

