import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class StorageHelper {
  static const String _versesKey = 'verses';
  static const String _progressKey = 'progress';
  static const String _userKey = 'user';
  static const String _lastBibleBookKey = 'last_bible_book';
  static const String _lastBibleChapterKey = 'last_bible_chapter';

  static Future<void> saveVerses(List<Map<String, dynamic>> verses) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_versesKey, jsonEncode(verses));
  }

  static Future<List<Map<String, dynamic>>> loadVerses() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_versesKey);
    if (data == null) return [];
    try {
      final decoded = jsonDecode(data) as List;
      return decoded.map((e) => e as Map<String, dynamic>).toList();
    } catch (e) {
      return [];
    }
  }

  static Future<void> saveProgress(List<Map<String, dynamic>> progress) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_progressKey, jsonEncode(progress));
  }

  static Future<List<Map<String, dynamic>>> loadProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_progressKey);
    if (data == null) return [];
    try {
      final decoded = jsonDecode(data) as List;
      return decoded.map((e) => e as Map<String, dynamic>).toList();
    } catch (e) {
      return [];
    }
  }

  static Future<void> saveUser(Map<String, dynamic> user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user));
  }

  static Future<Map<String, dynamic>?> loadUser() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_userKey);
    if (data == null) return null;
    try {
      return jsonDecode(data) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // Bible navigation persistence - DEPRECATED
  // Use BibleNavigationService instead for new implementations
  static Future<void> saveLastBibleLocation(int bookId, int chapter) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastBibleBookKey, bookId);
    await prefs.setInt(_lastBibleChapterKey, chapter);
  }

  static Future<Map<String, int>?> loadLastBibleLocation() async {
    final prefs = await SharedPreferences.getInstance();
    final bookId = prefs.getInt(_lastBibleBookKey);
    final chapter = prefs.getInt(_lastBibleChapterKey);

    if (bookId == null || chapter == null) return null;

    return {
      'bookId': bookId,
      'chapter': chapter,
    };
  }

  // Migration helper for BibleNavigationService
  static Future<void> migrateToBibleNavigationService() async {
    final prefs = await SharedPreferences.getInstance();

    // Clear old keys after migration
    await prefs.remove(_lastBibleBookKey);
    await prefs.remove(_lastBibleChapterKey);
  }
}
