import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:echoverse/services/bible_navigation_service.dart';

void main() {
  group('BibleNavigationService', () {
    late BibleNavigationService service;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      service = BibleNavigationService();
      await service.initialize();
    });

    tearDown(() {
      service.dispose();
    });

    group('Initialization', () {
      test('should initialize successfully', () {
        expect(service.isInitialized, isTrue);
      });

      test('should have default preferences', () {
        expect(service.shouldAutoRestore, isTrue);
        expect(service.maxHistoryItems, equals(50));
      });
    });

    group('Location Management', () {
      test('should update current location', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.updateCurrentLocation(location);

        expect(service.currentLocation, equals(location));
      });

      test('should add location to history', () async {
        final location1 = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );
        final location2 = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 2,
        );

        await service.updateCurrentLocation(location1);
        await service.updateCurrentLocation(location2);

        expect(service.readingHistory.length, equals(2));
        expect(service.readingHistory.first, equals(location2));
      });

      test('should limit history size', () async {
        // Set a small limit for testing
        await service.updateUserPreferences({'maxHistoryItems': 2});

        for (int i = 1; i <= 5; i++) {
          final location = BibleLocation(
            bookId: 1,
            bookName: 'Genesis',
            chapter: i,
          );
          await service.updateCurrentLocation(location);
        }

        expect(service.readingHistory.length, equals(2));
      });

      test('should remove duplicates from history', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.updateCurrentLocation(location);
        await service.updateCurrentLocation(location);

        expect(service.readingHistory.length, equals(1));
      });
    });

    group('Navigation Context', () {
      test('should auto-restore for app startup', () {
        expect(
          service.shouldAutoRestoreForContext(NavigationContext.appStartup),
          isTrue,
        );
      });

      test('should not auto-restore for back navigation', () {
        expect(
          service.shouldAutoRestoreForContext(NavigationContext.backNavigation),
          isFalse,
        );
      });

      test('should respect user preferences', () async {
        await service.updateUserPreferences({'autoRestore': false});

        expect(
          service.shouldAutoRestoreForContext(NavigationContext.appStartup),
          isFalse,
        );
      });
    });

    group('Reading Statistics', () {
      test('should calculate reading statistics', () async {
        // Add some reading history
        final today = DateTime.now();
        for (int i = 1; i <= 3; i++) {
          final location = BibleLocation(
            bookId: i,
            bookName: 'Book $i',
            chapter: 1,
            timestamp: today,
          );
          await service.updateCurrentLocation(location, addToHistory: true);
        }

        final stats = service.getReadingStatistics();

        expect(stats['chaptersToday'], equals(3));
        expect(stats['uniqueBooksThisMonth'], equals(3));
        expect(stats['currentStreak'], greaterThanOrEqualTo(1));
      });

      test('should calculate reading streak correctly', () async {
        final today = DateTime.now();
        final yesterday = today.subtract(const Duration(days: 1));

        // Add reading for today and yesterday
        await service.updateCurrentLocation(
          BibleLocation(
            bookId: 1,
            bookName: 'Genesis',
            chapter: 1,
            timestamp: today,
          ),
          addToHistory: true,
        );

        await service.updateCurrentLocation(
          BibleLocation(
            bookId: 1,
            bookName: 'Genesis',
            chapter: 2,
            timestamp: yesterday,
          ),
          addToHistory: true,
        );

        final stats = service.getReadingStatistics();
        expect(stats['currentStreak'], greaterThanOrEqualTo(2));
      });
    });

    group('Scroll Position Tracking', () {
      test('should update scroll position', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.updateCurrentLocation(location);
        await service.updateScrollPosition(1, 1, 100.0);

        expect(service.currentLocation?.scrollPosition, equals(100.0));
      });

      test('should not update scroll position for different location', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.updateCurrentLocation(location);
        await service.updateScrollPosition(2, 1, 100.0);

        expect(service.currentLocation?.scrollPosition, equals(0.0));
      });
    });

    group('Recent Books', () {
      test('should return recent books', () async {
        // Add reading history for different books
        for (int i = 1; i <= 5; i++) {
          final location = BibleLocation(
            bookId: i,
            bookName: 'Book $i',
            chapter: 1,
          );
          await service.updateCurrentLocation(location);
        }

        final recentBooks = service.getRecentBooks(limit: 3);
        expect(recentBooks.length, equals(3));
        expect(recentBooks.first.bookId, equals(5)); // Most recent first
      });

      test('should return unique books only', () async {
        // Add multiple chapters from same book
        for (int i = 1; i <= 3; i++) {
          final location = BibleLocation(
            bookId: 1,
            bookName: 'Genesis',
            chapter: i,
          );
          await service.updateCurrentLocation(location);
        }

        final recentBooks = service.getRecentBooks(limit: 5);
        expect(recentBooks.length, equals(1));
        expect(recentBooks.first.bookId, equals(1));
      });
    });

    group('Bookmarks', () {
      test('should add bookmark', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.addBookmark(location, note: 'Test bookmark');
        final bookmarks = await service.getBookmarks();

        expect(bookmarks.length, equals(1));
        expect(bookmarks.first['note'], equals('Test bookmark'));
      });

      test('should remove bookmark', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.addBookmark(location);
        await service.removeBookmark(location);
        final bookmarks = await service.getBookmarks();

        expect(bookmarks.length, equals(0));
      });
    });

    group('Data Persistence', () {
      test('should persist current location', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.updateCurrentLocation(location);

        // Create new service instance to test persistence
        final newService = BibleNavigationService();
        await newService.initialize();

        expect(newService.currentLocation?.bookId, equals(1));
        expect(newService.currentLocation?.chapter, equals(1));

        newService.dispose();
      });

      test('should persist reading history', () async {
        final location = BibleLocation(
          bookId: 1,
          bookName: 'Genesis',
          chapter: 1,
        );

        await service.updateCurrentLocation(location);

        // Create new service instance to test persistence
        final newService = BibleNavigationService();
        await newService.initialize();

        expect(newService.readingHistory.length, equals(1));
        expect(newService.readingHistory.first.bookId, equals(1));

        newService.dispose();
      });
    });

    group('Error Handling', () {
      test('should handle invalid book ID gracefully', () {
        final location = service.getLastLocationForBook(-1);
        expect(location, isNull);
      });

      test('should handle empty history gracefully', () {
        final recentBooks = service.getRecentBooks();
        expect(recentBooks, isEmpty);

        final stats = service.getReadingStatistics();
        expect(stats['chaptersToday'], equals(0));
        expect(stats['currentStreak'], equals(0));
      });
    });
  });
}
