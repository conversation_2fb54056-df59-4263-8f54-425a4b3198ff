import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Simple test to verify the Saved page UI components
void main() {
  group('SavedTab UI Tests', () {
    testWidgets('SavedTab basic UI elements are present', (WidgetTester tester) async {
      // Create a simple widget that mimics the SavedTab structure
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // Header
                const Padding(
                  padding: EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Saved',
                        style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Organize your verses and track progress',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),

                // Search bar
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.0),
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search verses...',
                      prefixIcon: Icon(Icons.search),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Section tabs
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Column(
                            children: [
                              Text('My Verses', style: TextStyle(fontWeight: FontWeight.bold)),
                              Text('3', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Column(
                            children: [
                              Text('Memorized'),
                              Text('1', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Column(
                            children: [
                              Text('Favorites'),
                              Text('1', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Sort options
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.0),
                  child: Row(
                    children: [
                      Text('Sort:', style: TextStyle(fontWeight: FontWeight.bold)),
                      SizedBox(width: 8),
                      Text('Newest'),
                      SizedBox(width: 16),
                      Text('Most Practiced'),
                      SizedBox(width: 16),
                      Text('Difficulty'),
                    ],
                  ),
                ),

                // Sample verse cards
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.all(24),
                    children: [
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'John 3:16',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'For God so loved the world that he gave his one and only Son...',
                                style: TextStyle(fontSize: 14),
                              ),
                              const SizedBox(height: 8),
                              LinearProgressIndicator(
                                value: 0.68,
                                backgroundColor: Colors.grey[300],
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                              ),
                              const SizedBox(height: 4),
                              const Text('68%', style: TextStyle(fontSize: 12, color: Colors.grey)),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Philippians 4:13',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'I can do all things through Christ who strengthens me.',
                                style: TextStyle(fontSize: 14),
                              ),
                              const SizedBox(height: 8),
                              LinearProgressIndicator(
                                value: 0.60,
                                backgroundColor: Colors.grey[300],
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                              ),
                              const SizedBox(height: 4),
                              const Text('60%', style: TextStyle(fontSize: 12, color: Colors.grey)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // Verify UI elements are present
      expect(find.text('Saved'), findsOneWidget);
      expect(find.text('Organize your verses and track progress'), findsOneWidget);
      expect(find.text('Search verses...'), findsOneWidget);
      expect(find.text('My Verses'), findsOneWidget);
      expect(find.text('Memorized'), findsOneWidget);
      expect(find.text('Favorites'), findsOneWidget);
      expect(find.text('John 3:16'), findsOneWidget);
      expect(find.text('Philippians 4:13'), findsOneWidget);
      expect(find.text('68%'), findsOneWidget);
      expect(find.text('60%'), findsOneWidget);
    });

    testWidgets('Search field is interactive', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Padding(
              padding: EdgeInsets.all(24.0),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Search verses...',
                  prefixIcon: Icon(Icons.search),
                ),
              ),
            ),
          ),
        ),
      );

      // Find and interact with search field
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);

      await tester.tap(searchField);
      await tester.enterText(searchField, 'John');
      await tester.pump();

      expect(find.text('John'), findsOneWidget);
    });
  });
}


