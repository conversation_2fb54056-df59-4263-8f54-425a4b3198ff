import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:echoverse/services/bible_navigation_service.dart';
import 'package:echoverse/screens/bible_books_screen.dart';
import 'package:echoverse/screens/bible_chapters_screen.dart';
import 'package:echoverse/screens/bible_reader_screen.dart';
import 'package:echoverse/models/bible_book.dart';

// Mock classes for testing
class MockBibleService {
  Future<List<BibleBook>> getOldTestamentBooks() async {
    return [
      BibleBook(id: 1, name: 'Genesis', testament: 1),
      BibleBook(id: 2, name: 'Exodus', testament: 1),
    ];
  }

  Future<List<BibleBook>> getNewTestamentBooks() async {
    return [
      BibleBook(id: 40, name: '<PERSON>', testament: 2),
      BibleBook(id: 41, name: 'Mark', testament: 2),
    ];
  }
}

void main() {
  group('Bible Navigation Flow Integration Tests', () {
    late BibleNavigationService navigationService;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      navigationService = BibleNavigationService();
      await navigationService.initialize();
    });

    tearDown(() {
      navigationService.dispose();
    });

    testWidgets('should navigate from books to chapters to reader', (tester) async {
      // Create test book
      final testBook = BibleBook(
        id: 1,
        name: 'Genesis',
        testament: 1,
      );

      // Test BibleBooksScreen
      await tester.pumpWidget(
        MaterialApp(
          home: BibleBooksScreen(
            navigationService: navigationService,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify BibleBooksScreen is displayed
      expect(find.byType(BibleBooksScreen), findsOneWidget);

      // Test navigation to chapters (simulated)
      await tester.pumpWidget(
        MaterialApp(
          home: BibleChaptersScreen(
            book: testBook,
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify BibleChaptersScreen is displayed
      expect(find.byType(BibleChaptersScreen), findsOneWidget);
      expect(find.text('Genesis'), findsOneWidget);

      // Test navigation to reader (simulated)
      await tester.pumpWidget(
        MaterialApp(
          home: BibleReaderScreen(
            book: testBook,
            chapter: 1,
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify BibleReaderScreen is displayed
      expect(find.byType(BibleReaderScreen), findsOneWidget);
      expect(find.text('Genesis 1'), findsOneWidget);
    });

    testWidgets('should preserve navigation state across screens', (tester) async {
      final testBook = BibleBook(
        id: 1,
        name: 'Genesis',
        testament: 1,
      );

      // Navigate to reader and update location
      await tester.pumpWidget(
        MaterialApp(
          home: BibleReaderScreen(
            book: testBook,
            chapter: 1,
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify navigation service has current location
      expect(navigationService.currentLocation?.bookId, equals(1));
      expect(navigationService.currentLocation?.chapter, equals(1));

      // Navigate back to books screen
      await tester.pumpWidget(
        MaterialApp(
          home: BibleBooksScreen(
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify state is preserved
      expect(navigationService.currentLocation?.bookId, equals(1));
      expect(navigationService.readingHistory.isNotEmpty, isTrue);
    });

    testWidgets('should handle auto-restore dialog', (tester) async {
      // Set up a previous reading location
      final location = BibleLocation(
        bookId: 1,
        bookName: 'Genesis',
        chapter: 5,
      );
      await navigationService.updateCurrentLocation(location);

      // Create a fresh navigation service to simulate app restart
      final freshService = BibleNavigationService();
      await freshService.initialize();

      await tester.pumpWidget(
        MaterialApp(
          home: BibleBooksScreen(
            navigationService: freshService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // The auto-restore dialog should appear (if conditions are met)
      // This test verifies the dialog can be handled properly
      if (find.text('Continue Reading?').evaluate().isNotEmpty) {
        expect(find.text('Continue Reading?'), findsOneWidget);
        expect(find.text('You were reading Genesis 5.'), findsOneWidget);

        // Test "Continue" button
        await tester.tap(find.text('Continue'));
        await tester.pumpAndSettle();
      }

      freshService.dispose();
    });

    testWidgets('should track reading statistics', (tester) async {
      final testBook = BibleBook(
        id: 1,
        name: 'Genesis',
        testament: 1,
      );

      // Simulate reading multiple chapters
      for (int chapter = 1; chapter <= 3; chapter++) {
        await tester.pumpWidget(
          MaterialApp(
            home: BibleReaderScreen(
              book: testBook,
              chapter: chapter,
              navigationService: navigationService,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Allow navigation service to update
        await tester.pump(const Duration(milliseconds: 100));
      }

      // Verify reading statistics
      final stats = navigationService.getReadingStatistics();
      expect(stats['chaptersToday'], greaterThanOrEqualTo(1));
      expect(stats['totalChaptersRead'], greaterThanOrEqualTo(3));
    });

    testWidgets('should handle scroll position restoration', (tester) async {
      final testBook = BibleBook(
        id: 1,
        name: 'Genesis',
        testament: 1,
      );

      // Create reader with scroll controller
      await tester.pumpWidget(
        MaterialApp(
          home: BibleReaderScreen(
            book: testBook,
            chapter: 1,
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the scroll view and simulate scrolling
      final scrollView = find.byType(SingleChildScrollView);
      if (scrollView.evaluate().isNotEmpty) {
        await tester.drag(scrollView, const Offset(0, -200));
        await tester.pumpAndSettle();

        // Allow scroll position to be saved
        await tester.pump(const Duration(milliseconds: 600));

        // Verify scroll position is tracked
        expect(navigationService.currentLocation?.scrollPosition, greaterThan(0));
      }
    });

    testWidgets('should handle navigation context changes', (tester) async {
      // Test tab switch context
      await navigationService.startNavigationSession(NavigationContext.tabSwitch);

      await tester.pumpWidget(
        MaterialApp(
          home: BibleBooksScreen(
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test direct navigation context
      await navigationService.startNavigationSession(NavigationContext.directNavigation);

      final testBook = BibleBook(
        id: 1,
        name: 'Genesis',
        testament: 1,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BibleChaptersScreen(
            book: testBook,
            navigationService: navigationService,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify navigation session is tracked
      expect(navigationService.currentSession, isNotNull);
    });

    testWidgets('should handle memory cleanup on dispose', (tester) async {
      final testBook = BibleBook(
        id: 1,
        name: 'Genesis',
        testament: 1,
      );

      // Create and dispose multiple screens to test memory management
      for (int i = 0; i < 3; i++) {
        await tester.pumpWidget(
          MaterialApp(
            home: BibleReaderScreen(
              book: testBook,
              chapter: i + 1,
              navigationService: navigationService,
            ),
          ),
        );

        await tester.pumpAndSettle();
      }

      // Pump empty widget to trigger dispose
      await tester.pumpWidget(const MaterialApp(home: SizedBox()));
      await tester.pumpAndSettle();

      // Verify navigation service still works after screen disposal
      expect(navigationService.isInitialized, isTrue);
      expect(navigationService.readingHistory.isNotEmpty, isTrue);
    });

    testWidgets('should handle error states gracefully', (tester) async {
      // Test with invalid book data
      final invalidBook = BibleBook(
        id: -1,
        name: '',
        testament: 0,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BibleChaptersScreen(
            book: invalidBook,
            navigationService: navigationService,
          ),
        ),
      );

      // Should not crash
      await tester.pumpAndSettle();
      expect(find.byType(BibleChaptersScreen), findsOneWidget);
    });
  });
}
