#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Script to verify the Saved page functionality and Firebase integration
void main() async {
  print('🔍 Verifying Saved Page Functionality...\n');
  
  // Check Firebase configuration
  await checkFirebaseConfiguration();
  
  // Check Firestore rules
  await checkFirestoreRules();
  
  // Check service implementations
  await checkServiceImplementations();
  
  // Check UI components
  await checkUIComponents();
  
  // Check test coverage
  await checkTestCoverage();
  
  print('\n✅ Saved Page Verification Complete!');
}

Future<void> checkFirebaseConfiguration() async {
  print('📱 Checking Firebase Configuration...');
  
  // Check firebase_options.dart
  final firebaseOptionsFile = File('lib/firebase_options.dart');
  if (await firebaseOptionsFile.exists()) {
    final content = await firebaseOptionsFile.readAsString();
    if (content.contains('projectId') && content.contains('apiKey')) {
      print('  ✅ Firebase options configured correctly');
    } else {
      print('  ❌ Firebase options missing required fields');
    }
  } else {
    print('  ❌ Firebase options file not found');
  }
  
  // Check pubspec.yaml for Firebase dependencies
  final pubspecFile = File('pubspec.yaml');
  if (await pubspecFile.exists()) {
    final content = await pubspecFile.readAsString();
    final requiredDeps = ['firebase_core', 'firebase_auth', 'cloud_firestore'];
    bool allDepsPresent = true;
    
    for (final dep in requiredDeps) {
      if (content.contains(dep)) {
        print('  ✅ $dep dependency found');
      } else {
        print('  ❌ $dep dependency missing');
        allDepsPresent = false;
      }
    }
    
    if (allDepsPresent) {
      print('  ✅ All Firebase dependencies present');
    }
  }
}

Future<void> checkFirestoreRules() async {
  print('\n🔒 Checking Firestore Security Rules...');
  
  final rulesFile = File('firestore.rules');
  if (await rulesFile.exists()) {
    final content = await rulesFile.readAsString();
    
    // Check for essential security patterns
    final securityChecks = [
      {'pattern': 'isAuthenticated()', 'description': 'Authentication check function'},
      {'pattern': 'isOwner(', 'description': 'Ownership validation function'},
      {'pattern': 'users/{userId}', 'description': 'User document security'},
      {'pattern': 'favorites/{verseId}', 'description': 'Favorites subcollection security'},
      {'pattern': 'progress/{progressId}', 'description': 'Progress subcollection security'},
    ];
    
    for (final check in securityChecks) {
      if (content.contains(check['pattern']!)) {
        print('  ✅ ${check['description']} implemented');
      } else {
        print('  ⚠️  ${check['description']} not found');
      }
    }
  } else {
    print('  ❌ Firestore rules file not found');
  }
}

Future<void> checkServiceImplementations() async {
  print('\n🔧 Checking Service Implementations...');
  
  // Check FirestoreService
  final firestoreServiceFile = File('lib/services/firestore_service.dart');
  if (await firestoreServiceFile.exists()) {
    final content = await firestoreServiceFile.readAsString();
    
    final methods = [
      'addToFavorites',
      'removeFromFavorites',
      'getFavorites',
      'saveProgress',
      'getProgress',
      'progressStream',
      'favoritesStream'
    ];
    
    for (final method in methods) {
      if (content.contains(method)) {
        print('  ✅ FirestoreService.$method implemented');
      } else {
        print('  ❌ FirestoreService.$method missing');
      }
    }
  }
  
  // Check VerseService
  final verseServiceFile = File('lib/services/verse_service.dart');
  if (await verseServiceFile.exists()) {
    final content = await verseServiceFile.readAsString();
    
    final methods = [
      'setCurrentUser',
      'getFavoriteVerses',
      'addToFavorites',
      'removeFromFavorites',
      'getAllVerses'
    ];
    
    for (final method in methods) {
      if (content.contains(method)) {
        print('  ✅ VerseService.$method implemented');
      } else {
        print('  ❌ VerseService.$method missing');
      }
    }
  }
  
  // Check ProgressService
  final progressServiceFile = File('lib/services/progress_service.dart');
  if (await progressServiceFile.exists()) {
    final content = await progressServiceFile.readAsString();
    
    final methods = [
      'getMasteredVerses',
      'getProgressForVerse',
      'updateProgress',
      'incrementPractice',
      'progressStream'
    ];
    
    for (final method in methods) {
      if (content.contains(method)) {
        print('  ✅ ProgressService.$method implemented');
      } else {
        print('  ❌ ProgressService.$method missing');
      }
    }
  }
}

Future<void> checkUIComponents() async {
  print('\n🎨 Checking UI Components...');
  
  final savedTabFile = File('lib/screens/saved_tab.dart');
  if (await savedTabFile.exists()) {
    final content = await savedTabFile.readAsString();
    
    final uiElements = [
      {'pattern': 'My Verses', 'description': 'My Verses tab'},
      {'pattern': 'Memorized', 'description': 'Memorized tab'},
      {'pattern': 'Favorites', 'description': 'Favorites tab'},
      {'pattern': 'Search verses', 'description': 'Search functionality'},
      {'pattern': 'SortOption', 'description': 'Sort options'},
      {'pattern': 'VerseCard', 'description': 'Verse card component'},
    ];
    
    for (final element in uiElements) {
      if (content.contains(element['pattern']!)) {
        print('  ✅ ${element['description']} implemented');
      } else {
        print('  ❌ ${element['description']} missing');
      }
    }
  }
}

Future<void> checkTestCoverage() async {
  print('\n🧪 Checking Test Coverage...');
  
  final testFiles = [
    'test/screens/saved_tab_test.dart',
    'test/integration/saved_page_firebase_test.dart',
  ];
  
  for (final testFile in testFiles) {
    final file = File(testFile);
    if (await file.exists()) {
      print('  ✅ $testFile exists');
    } else {
      print('  ❌ $testFile missing');
    }
  }
  
  // Check if tests can run
  print('\n  Running basic UI tests...');
  final result = await Process.run('flutter', ['test', 'test/screens/saved_tab_test.dart']);
  
  if (result.exitCode == 0) {
    print('  ✅ UI tests pass');
  } else {
    print('  ❌ UI tests failed');
    print('  Error: ${result.stderr}');
  }
}
