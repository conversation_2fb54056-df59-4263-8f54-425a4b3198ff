{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987ce8d4b25ecea26cf2edab24df048ed4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2215f65f2137bf2cfcb220dcd53ce43", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2215f65f2137bf2cfcb220dcd53ce43", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d953939b4e072657cb497566a1a87ab4", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9aa03af1b1a570e49c5a9c13b238745", "guid": "bfdfe7dc352907fc980b868725387e98729151267dd9ec3e9ef6758ad70b4b5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e38815914a419849dd0ba6873236a31", "guid": "bfdfe7dc352907fc980b868725387e98ce5890efc6928e8d8b4f2c7985fde114", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98163d5bb4d6b20d96d6e188993d86a7d5", "guid": "bfdfe7dc352907fc980b868725387e9883956de5c6920bf95e344a9c8fb1fe36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c223a783c2df9a2bb339183103e650", "guid": "bfdfe7dc352907fc980b868725387e98bee9f277be69487b533d7f2ebe2150d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbeacd18647b9bb6ca98f875d20903a0", "guid": "bfdfe7dc352907fc980b868725387e9847ef489ba647d6362622a3d6b286e61e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb4c786938856732d391babfa3595119", "guid": "bfdfe7dc352907fc980b868725387e9825cca6297fab09695982a49790d807c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96d1ff20cbc68d55bc4ebd7d8afc17f", "guid": "bfdfe7dc352907fc980b868725387e98b65e23be43f71ba980e66f567200e8fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112161648292affc5d48adfc87b304aa", "guid": "bfdfe7dc352907fc980b868725387e98690c9995808aea65ddd0ebcf69a7ae34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ed44f604287e01f473ddb63d4d4d54", "guid": "bfdfe7dc352907fc980b868725387e98eab606235c4f8dea8caf1d9d717b7a8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf30a6684087411cd3d0d0dac21901f", "guid": "bfdfe7dc352907fc980b868725387e9890961e63beb2ce6e729025a5dc52569f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98482af227f2f47f9ed2c1f3cba7c6f2f5", "guid": "bfdfe7dc352907fc980b868725387e9857007f2c9acae62a8635d6461da12b83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a3e59d73039a883eabef548324ad5b2", "guid": "bfdfe7dc352907fc980b868725387e98e47c02355151fcaf3b747a189f6e227a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98088b47594378526875458603baf9006f", "guid": "bfdfe7dc352907fc980b868725387e9887115f0a43c82d75bdbc34e6611cd286", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989bfdfa191b547fe7be4616f4daa8fd03", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e867503028a4946d4ef79aa2ab6de4f3", "guid": "bfdfe7dc352907fc980b868725387e981a338804629027cc389da289f78f0214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a70e3e209bd67f9cfeacd2b281974d", "guid": "bfdfe7dc352907fc980b868725387e981b9c6cba91774b4c846e810ce126b81b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814121f21f0f17e904614c070bfd3a00b", "guid": "bfdfe7dc352907fc980b868725387e9869833a6bf46b1c7f56e0a4dc2b128531"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb55ecad25e32910bfdfe5a1988cba8", "guid": "bfdfe7dc352907fc980b868725387e985e7be004a8abdec1c0d4bf8b1b6174f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96e9f45b98abfb6c834fb955bcecd7d", "guid": "bfdfe7dc352907fc980b868725387e98f04156ad7eacb7c014e915501b1d1c0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c585d478bdad209006828975dea4320", "guid": "bfdfe7dc352907fc980b868725387e98433b72f10e9afa76d13b2153c09f62ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04679fee5a808b2c14147bbb4b13589", "guid": "bfdfe7dc352907fc980b868725387e988850fad5cb19004d19717ab336fc9c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51072de90c6a2a77164f23afc958d00", "guid": "bfdfe7dc352907fc980b868725387e985717e8b1f44d29a42c1b0380c09feddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b05c648de6d5d4b38d8531d9d72fa8a", "guid": "bfdfe7dc352907fc980b868725387e9851f8dd52e65b5f52b6c4a9e5a28c1bf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cbc910c29ed19b1f31cd82c1550bb52", "guid": "bfdfe7dc352907fc980b868725387e981847e4ccce078dbde1f710807c1afe12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864deb8ffc7368d7824142444b6491d65", "guid": "bfdfe7dc352907fc980b868725387e985cc3740e4cb55b343dca1aaa4d4e1165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981638eb060b02f3d917da5e66a1388820", "guid": "bfdfe7dc352907fc980b868725387e98310858803087277161535ddbf30344db"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}