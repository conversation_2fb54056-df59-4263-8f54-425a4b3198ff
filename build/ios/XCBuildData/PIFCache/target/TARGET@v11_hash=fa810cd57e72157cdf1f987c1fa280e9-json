{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c313a90a67f6e36ef5138b4ba4abfc90", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98540fed615e84cd392c9196b22123eb12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3368d45bcd73901755506b57f53f61a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980755843c208b5392bb7fb93025b6f2f6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3368d45bcd73901755506b57f53f61a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988466f57893ce351dada764fd61a3c5ce", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc1ec51c456acb337d45b380ad14f4be", "guid": "bfdfe7dc352907fc980b868725387e98bd788c6125ac3640d97ae82e72969df0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168914226a5d34e02d854f5afa3610bb", "guid": "bfdfe7dc352907fc980b868725387e980aee952cd8c0b7f6bb1b8e288d2b851b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d6275cb3f871ba19bde9661f8ef265f", "guid": "bfdfe7dc352907fc980b868725387e980aab337ac8c14167813168bce08917d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465c0b18a789640f2aa4fda8cf0196d1", "guid": "bfdfe7dc352907fc980b868725387e98bb31cc825ad7dcd11c244c4fae49cb6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143f7ee1baefcf964930413cd7239e2d", "guid": "bfdfe7dc352907fc980b868725387e98a61c6a55021c5f5c193270ede5cbcc5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98481367af98690692591ba36f74c5cd3b", "guid": "bfdfe7dc352907fc980b868725387e98047a3c62674d04bd643e99b97ed13827", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d1923b561c27cfbdd62b9e44f9775868", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cbc89b4145ecccb44e4cb7e44526da18", "guid": "bfdfe7dc352907fc980b868725387e9827ae2bd428dac7be9ccfddffa784d35f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807719a4d936d051f64ed7433d17521c", "guid": "bfdfe7dc352907fc980b868725387e98e5b31b940e514a802e9825595c09acdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d98f443c428f551257fb93a1f0f7edf", "guid": "bfdfe7dc352907fc980b868725387e9855e000144ebce767f1193d6ea0e9bad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3f7d20ebb8807e1a69061ffd584f90", "guid": "bfdfe7dc352907fc980b868725387e9861a1f3da0a6c80b0c979ba18d6537d24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f30be2a6dfd97ffdbc1d594aa0e663", "guid": "bfdfe7dc352907fc980b868725387e98afc2a92113cdb12ce0281fde6a2b3fb1"}], "guid": "bfdfe7dc352907fc980b868725387e98e2cef0239dec59b07f816244beaf4a30", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e98a41f2f0785e8ac541693236c8e3df45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98316e3fc14c77f397e2bffb626e1f9142", "guid": "bfdfe7dc352907fc980b868725387e984c9a69230460043f3f33cf49937f4a73"}], "guid": "bfdfe7dc352907fc980b868725387e98e4f39f2c9a0b54af73000e84bd7026ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98866207b6c28fdaa7a696d009acddb999", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9872d8b18398a472df323e97ed4041bed4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}