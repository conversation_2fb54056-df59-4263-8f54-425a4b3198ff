{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2e3641bc8e55d45fdd49c471d66ed09", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808a8b67e38627e044a28c5e23a46e525", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a3b7125beea6186f28f031a4055f6e0a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b4fcdfbcfa3f39373499072b3f6c9f02", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a3b7125beea6186f28f031a4055f6e0a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983306b2daf670bc0a0047e97106693d9c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831ee12ca979004da93c84f61ca87c6ca", "guid": "bfdfe7dc352907fc980b868725387e988152cff0ecc1d4187aba666056a0c35d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986d1e206c43ad0ab36421b12ed0f525fa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b7b905c373644bb9847381f4bccf6c9", "guid": "bfdfe7dc352907fc980b868725387e980fa05f64f57bf33b170cf87c3848217c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c61f0f02a565835c746de243d2ca7594", "guid": "bfdfe7dc352907fc980b868725387e984ee89436227c94e051ddd6cfbac2569d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98518d2d7775378184a3daea7222fb480a", "guid": "bfdfe7dc352907fc980b868725387e9808cde8a5dc6d94503193ae44e6785884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e4a2726dd3ddf989b0a93bae27217c", "guid": "bfdfe7dc352907fc980b868725387e986d7b5e7c68c9725844a0ca6b88498b38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be0f06d813d33b07b74b1c3879e3d38a", "guid": "bfdfe7dc352907fc980b868725387e98d70021b1569042ad5e27a037a6878264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1c5cee5eb9acc3d8eada13460b673f9", "guid": "bfdfe7dc352907fc980b868725387e9881fda4a8b9aab5368e221b8c0ca2959a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c717c4ae0bb7b001fe6f01e9bece46", "guid": "bfdfe7dc352907fc980b868725387e983ebd70ef2c1777276a73ec478955dfdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44249260a38a118ba954bfcaa76537a", "guid": "bfdfe7dc352907fc980b868725387e9877bc4cb04ea1c7b2061603d1a3e76afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985317ec02ea35f56b29878fa5d5961023", "guid": "bfdfe7dc352907fc980b868725387e988f9fd73170bb923b02282ea289d38ef2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0414d39f395bd2833d4ed5d7dd0ae1b", "guid": "bfdfe7dc352907fc980b868725387e98c33410960b81ca1cad33bb3ef14e2346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c989aef46e72e6864fb7ae4dcbef4659", "guid": "bfdfe7dc352907fc980b868725387e98185777d9b5997e3e114f4758513beceb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d929974b42e81ae3a0df12d67ad8c176", "guid": "bfdfe7dc352907fc980b868725387e98026acb7d8b5331d0861452a7fcb054cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506d1bb705fdad340cc034f68e289266", "guid": "bfdfe7dc352907fc980b868725387e98acfba6b8f673031e0a38c3f2300779a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de059cedf9d4bfc40c6272a96c876018", "guid": "bfdfe7dc352907fc980b868725387e98be42ad072010258faf1433f5093b4855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fefb365ad2518d52fa65fc8830a1c1d4", "guid": "bfdfe7dc352907fc980b868725387e98c94b74002a237e85b25d2ca8b7cc554f"}], "guid": "bfdfe7dc352907fc980b868725387e98e9a7dc2965430b5bec6d969408271615", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e98993d5cd91fd18de879c9ad8e78d14664"}], "guid": "bfdfe7dc352907fc980b868725387e98b87a1f2d2d6dc80db51e69e866211c45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98757aa21b3e49968762a5a08f34559430", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e986b4dff63d001b2785a6f6694914363e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}