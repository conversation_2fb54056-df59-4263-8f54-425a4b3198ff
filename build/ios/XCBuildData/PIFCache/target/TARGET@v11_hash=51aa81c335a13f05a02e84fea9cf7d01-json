{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98006fd5a559de3316b5ba61abaff59c1e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98009c6120ba24febd33ed37c005ad79e9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873238786be9d6c02eebfc80bdcea24ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980e31fa8b5e6cac0e885b546f1a4a2632", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873238786be9d6c02eebfc80bdcea24ae", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f4968a89e77e9bf4187c658c728e6f37", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808220ec615c05fcc540e9c4e45eaf049", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98348833a4d5083c89561f14aec023547f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9892f820426859a0ce260522f213091557", "guid": "bfdfe7dc352907fc980b868725387e98dba2f57b3c57ed3f2d4eb4daead865ca"}], "guid": "bfdfe7dc352907fc980b868725387e9873f3d65dd97e6e2ebcc693eb37334e61", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984fe1f454389a944b317683cfdba2e41e", "name": "leveldb-library-leveldb_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980fe361aa6bc047147e11f30537be10ee", "name": "leveldb_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}