{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a9b36ba24330f9697b653122d5076720", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98575c89e13940d8eb25b2837a6500d2f7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1c7d767f4194192d1bf0a3a08d53e13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98952447f1a13ef149df0b536ee17ed1ee", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1c7d767f4194192d1bf0a3a08d53e13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a58527430d5bfe01fe542eb4d25afc8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dba2a7cbe96fc1216d74aca4d6266d32", "guid": "bfdfe7dc352907fc980b868725387e98be9f311ca4ba70522055507f22318af0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04b994a21f41e8971060128b210c4ee", "guid": "bfdfe7dc352907fc980b868725387e984c8425c8425a319dae9a8e8253b21730", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98054f1a8364eef8c8cc2ea5f338f2e524", "guid": "bfdfe7dc352907fc980b868725387e9875ea2e7f302a619e61327a8bed8200e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a35706d56bc9398828fe615bf7ab00d", "guid": "bfdfe7dc352907fc980b868725387e98e53fb7d90b4930cf5425c8b60c7e9714", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e834e26afaf8ccdb6e6d0f32365c2b3", "guid": "bfdfe7dc352907fc980b868725387e98c4456cee21198b45da1f2945289d3649", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986f08b3dd204a84f35cc65cba53c23207", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2b94fa49bd5953a4b67deb7fce8a80c", "guid": "bfdfe7dc352907fc980b868725387e98f07d203ef7f1689b1ca8a767fec4ddd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b58bed6bef8b17566f1359a43dd2aaf", "guid": "bfdfe7dc352907fc980b868725387e98dbcba1bcdc649837f74a126918200c71"}], "guid": "bfdfe7dc352907fc980b868725387e98aa0304d8d08e1e817b05478fdea87ee2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e985b6c205f30d32baccee03c5f1a672349"}], "guid": "bfdfe7dc352907fc980b868725387e98a3da72a84fcabd2b46189b6c1a03ae52", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9864ac61ede7bc69a2f1c0258c799559c9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98da29652de71b686743df2bd56decf7ef", "name": "RecaptchaInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988c2a56cd963a48a63ab689fe60f47236", "name": "RecaptchaInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}