{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ab7888a8cdb019c22be1ae9a3f97117", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845ab71acb007df124324acb5b0cb87d4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98192474384724e115275896d5f8d41433", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b4f3b80ff7c1436b7fa9b066b0668e78", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98192474384724e115275896d5f8d41433", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987cf62609c35142c1384aef8d1bb8f993", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98064cb79a4370f90c6c5e88fbc6192b1a", "guid": "bfdfe7dc352907fc980b868725387e98ac9ebb86d9bb772ddf9f7f31bc71f9ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba1b67afc980e42349dbf21c1eda8b9b", "guid": "bfdfe7dc352907fc980b868725387e9896a41800eab0efd019bb7d1bb88c2d9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb75e8ed6a3226a99c3759702a354511", "guid": "bfdfe7dc352907fc980b868725387e98d89d0c1c156bd93e522b7c3a9364cc81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a68b2b58e2e96044d03c3cd452640c9", "guid": "bfdfe7dc352907fc980b868725387e985ad5e7c7fa8367bab8c7993edf5f6f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859271b27af965836e9a439b157771489", "guid": "bfdfe7dc352907fc980b868725387e98d1bedebbbd079f952d896de85daee4e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98930ccb4de5cb7498a00786165756831b", "guid": "bfdfe7dc352907fc980b868725387e98e1a2d1ba4a7ebbf6ecfa204e1956f4a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ec81a58fc65afa2ebc0d1ca151b9aee", "guid": "bfdfe7dc352907fc980b868725387e98d3b2f730687581a1336cdfa452e08876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f036965d4355c6a3b618cdbc4c1dc015", "guid": "bfdfe7dc352907fc980b868725387e98108f9146458c7804348d536e95d99a95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a0e4d8166bb613d41914422fbed3af", "guid": "bfdfe7dc352907fc980b868725387e98cfb5d97e36ea0b2fb7a9810ace0ff9c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98924147556854742d6136c44185e02370", "guid": "bfdfe7dc352907fc980b868725387e988b8cf989a6b9c360ba6baf70ff5e2cd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b675fc2f42549fea7c43412a45abee1", "guid": "bfdfe7dc352907fc980b868725387e983bd24052fac452f96040f5f79942bc26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3ce97a81f188f0e6dfac566fad837f0", "guid": "bfdfe7dc352907fc980b868725387e9854d1edc27ff5376aff900b99978b602a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b5fd3f17047a4c665869b42fe43a044", "guid": "bfdfe7dc352907fc980b868725387e98d137927ba0836e8a7c2af9dfe4907b01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da57607b3385f411c210b74fcdb329e4", "guid": "bfdfe7dc352907fc980b868725387e98b798a85c9d25a2886b2349f3080f0e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98871cef147fc6ac59237026e133057331", "guid": "bfdfe7dc352907fc980b868725387e98145d24970767a279edf53bd51db88a52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871c89e07f93b11cfb1de090f1deeb2ad", "guid": "bfdfe7dc352907fc980b868725387e989ce51b8eaebad820952fadd771169259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989347024268e1857990bee01003208c1d", "guid": "bfdfe7dc352907fc980b868725387e98192f82dc136f74fc8b0d2cff9813e840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46b996aed685cda467b4f30b7a50784", "guid": "bfdfe7dc352907fc980b868725387e9803451e18b10152f1a0efaf570df3e189", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836bfbdab9e47f4a53d37b6bc6882c319", "guid": "bfdfe7dc352907fc980b868725387e9842b58ba5e9a39f456f743ae1a0beda15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e36c33b66b586f0e6b9bad15c5cf76a", "guid": "bfdfe7dc352907fc980b868725387e981c49882bea5b7ee18d61bd1a9f798620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819fcac614e5ae452dc9b026356d87ae3", "guid": "bfdfe7dc352907fc980b868725387e9829d728deba87169f3f5861d4304c783b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f0d1a53e881ee33157a4fbedf9d430", "guid": "bfdfe7dc352907fc980b868725387e982afd3f2eadb16ccda3eed620c82bcd49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9dbb8dbab29fafd427be30f52263834", "guid": "bfdfe7dc352907fc980b868725387e981bc4b646027959d1ec1a7480e2060641", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988b6154cf668d2c5a2d0cf479bf3688fd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986b09c555e7ad1f0d5133c6e6f70528dc", "guid": "bfdfe7dc352907fc980b868725387e98a04ed68abdafc77e27714f7649a97e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d3073c72d5565e14aba3fb391e697c2", "guid": "bfdfe7dc352907fc980b868725387e9873334b1a59d0336825d23cec72badbc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4ce4383035db6164c11255a24bd63aa", "guid": "bfdfe7dc352907fc980b868725387e986dab95eb24b6b36d6700bfc3cb683dae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d218c9fced3c030c02c9ef13bfd945a", "guid": "bfdfe7dc352907fc980b868725387e98ecb040f91fd3274b6718e39611297d3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f275bffbce434583abc9b2712b4e7db", "guid": "bfdfe7dc352907fc980b868725387e98bc4104391971ec68daf64944627977b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e0ad41ac5c28cbefb434e785445abe", "guid": "bfdfe7dc352907fc980b868725387e9815d6795f48ce71ee4c0fe9f203a0a3d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2ffcea007818e4f216dac515ab9aaf", "guid": "bfdfe7dc352907fc980b868725387e98f3204a10282d2007b463ce5ea97f0d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987527b2886afcfe529deb0c5197f3bd00", "guid": "bfdfe7dc352907fc980b868725387e98cdd96da8291a67acca3efc18f7bd766b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a55848359443ae97af9014772735133", "guid": "bfdfe7dc352907fc980b868725387e9877900b64b839c6275e66edc9c4705871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6137e122ddd6ca7400fff1ed813a08", "guid": "bfdfe7dc352907fc980b868725387e984b05b024cc02ff445c7b9dfebdff03df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e010071a3d8613ab9d19bbe02c8f391", "guid": "bfdfe7dc352907fc980b868725387e981a035427c3b5566876630562576a8c99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98165791ddf83381f1355118008756f71a", "guid": "bfdfe7dc352907fc980b868725387e9886ed3984e3936073f006a4b8832f83cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da38c60bec5068cb96fb7527b7cc5e6d", "guid": "bfdfe7dc352907fc980b868725387e98e62f243a50ef5ea7ab0c7956a6934590"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980419d62ed5f7cbde40884783dc366bd7", "guid": "bfdfe7dc352907fc980b868725387e985791954dc7497063a9e424a7c159125c"}], "guid": "bfdfe7dc352907fc980b868725387e9831210f5de565f49ccef54d7630d3c314", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e98e8c188043c2e217299612fc846d8e35d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32d740ab7837ea07e213be8bbd62593", "guid": "bfdfe7dc352907fc980b868725387e98feae312fbf370b1f2bc477dbd5fcefe9"}], "guid": "bfdfe7dc352907fc980b868725387e98ead03a7f7337359cece9d508868c572e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c83ebe7933636eae6e22b5a8e24cca6b", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98161937f04c6ac983b422693356f2f8d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}