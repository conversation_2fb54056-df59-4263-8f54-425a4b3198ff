{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e84ecb2489ac4a6bfe6df9a2c5656e93", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d33a8a5aae051ac21d2b64132a3bd927", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983846aed86d94f103d018fe35d528864a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9081b6697a2cf9b15e75c4fe792ca74", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983846aed86d94f103d018fe35d528864a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e0e586c69e3afba01c3515bbfdb4d057", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c2e36abb244b5b946f3a2d37f301df57", "guid": "bfdfe7dc352907fc980b868725387e9887b2f300574821940e71a8ab39d59d02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64cb461c5b91dd3e096af357af7c618", "guid": "bfdfe7dc352907fc980b868725387e98622c7209d8f90d541fa8dc6ee9053433", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2db67288f0c35cef2e52197d39eefba", "guid": "bfdfe7dc352907fc980b868725387e980a7ab50b4b0432a771558ba7f639e876", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a6f653eafad3f9cab52f0fa4791551", "guid": "bfdfe7dc352907fc980b868725387e98e8082037e42c03587b1606189a17fe76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6d24d0e12524b6a7ef173066b17b53", "guid": "bfdfe7dc352907fc980b868725387e98f6d1234cf072450230cd34f4e0e8950f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eae27610514e98438676c99178e60ad0", "guid": "bfdfe7dc352907fc980b868725387e9849ef7158da5cd3a8761346956233d469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6fa04da05c9bd626f917527588a712a", "guid": "bfdfe7dc352907fc980b868725387e98960e27709db5a19e74949e3387a18a6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5d94439b66a42f696b2d6af045e77e", "guid": "bfdfe7dc352907fc980b868725387e989a77ba6c5cfe9884b38ac40a5898203c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98182978045c725fe6f784049c0f6d0d81", "guid": "bfdfe7dc352907fc980b868725387e9844542d65e07691e2fbed66f5f8222503", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e12ea23b84139bb370276832968c182", "guid": "bfdfe7dc352907fc980b868725387e9853d1a7d528aa3fe974018c9e66840187", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f32ba3acaf5d7d1ef61a32433016b74", "guid": "bfdfe7dc352907fc980b868725387e98e2c3f0c7c5faac40b076fea04b78e71a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d4faee81fbecdf96126b841c20f057", "guid": "bfdfe7dc352907fc980b868725387e98e26dc19e4a04a5919f5cef40537e10f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4029607343a15a531ae5788cee744f4", "guid": "bfdfe7dc352907fc980b868725387e98ac0a471c18eb1a8fa1d57510f1777e0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871c5641d9999ed32b76b787152cebcbd", "guid": "bfdfe7dc352907fc980b868725387e98fdc8c7d8c73bce7f2acab31e89d8b496", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98660afd79082a1ddcfc4663a12b87bdb7", "guid": "bfdfe7dc352907fc980b868725387e98c140916027c294469871c006c36bd839", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2a8d3817723b87e1567ef3ab134475", "guid": "bfdfe7dc352907fc980b868725387e986f693d2cb9f202563381ae45ce5e0980", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa6e11e115aee45c1576327bdd42b08", "guid": "bfdfe7dc352907fc980b868725387e98a954733bfe7c6d947ed43c17fd16d804", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866997a0444b73a290ede1c4f01feea62", "guid": "bfdfe7dc352907fc980b868725387e9865e2c294dde905a077582583f3747d38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b51c0353aced42e05aeafbeb967df4", "guid": "bfdfe7dc352907fc980b868725387e98233a5113d644e0cb16b99854fd9a5a33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987511ca49c92d3545147fbddced5c03e2", "guid": "bfdfe7dc352907fc980b868725387e984ec64bf56a0931c100ff32441baa5adc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859da11083121d8ad0256a3ded8048a61", "guid": "bfdfe7dc352907fc980b868725387e987d7178e7e3c2992c289d7470cae08057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29a0fd6a80f3cd2f8597946574635f2", "guid": "bfdfe7dc352907fc980b868725387e98e2fc03aff8ae1e98137cbfe9c2161c57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7d7d3bf49711c94dac4f3f2f38256e7", "guid": "bfdfe7dc352907fc980b868725387e98e0987de5b66f9f75875157173e3718d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0d983170cbca07686a8139203c0a29d", "guid": "bfdfe7dc352907fc980b868725387e98282f6ae04305ccaf9855b3c48a5a14ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec7b7e1cd23f1ff3ac6d90dc1d0736b4", "guid": "bfdfe7dc352907fc980b868725387e98ba7375af226515cf448dbb8b05920d03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98161c57d4464e8e4c46027ac195da3f3d", "guid": "bfdfe7dc352907fc980b868725387e98fc4969d808b730b1cc13f872711a1ecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e4ab7859547f04798b39780b11dc833", "guid": "bfdfe7dc352907fc980b868725387e987de27021266e6012fe8dc9c5976b5b70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085e41316b9aec91fbe841af0efb3ad5", "guid": "bfdfe7dc352907fc980b868725387e9833a32a3ed31d9ae43aad6ab81b6c0955", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba7e651c614916375c3d864acc3bdb58", "guid": "bfdfe7dc352907fc980b868725387e98a3717f19edaee79fa0639e1e372ca50d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984408296df4fa75d5091657faa5d38b74", "guid": "bfdfe7dc352907fc980b868725387e9822624f8b0da6cb2688eb508710ae76e2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984b18f3e9de111a0fb11d66afe6bf40b3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa221a93736259f2c4762668703e5cf5", "guid": "bfdfe7dc352907fc980b868725387e9802acd1dee4e5c524a8795b582e6da405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff93b96e5a417bb905900e79b08cdfe6", "guid": "bfdfe7dc352907fc980b868725387e989d0122af2d4cebd67a2429d34ba7dd00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986359598f06f7eb421a0ed4726dbaa819", "guid": "bfdfe7dc352907fc980b868725387e9844732ff9711e1b3cd9189231b445eb89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1f36294ea4fb698f5f621456331624", "guid": "bfdfe7dc352907fc980b868725387e983ef70ce8de9d4ab4ab331e3d157a8a27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980440a73f9331866350c8f9465a626267", "guid": "bfdfe7dc352907fc980b868725387e98263c7ac41217cd469f04119ff878f9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980777549f1e4f491cc4969ffe72cbbc63", "guid": "bfdfe7dc352907fc980b868725387e98d86ede9846acccfb11383c38c91b732f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328e92bb46f9a8c2d8d2900540be912c", "guid": "bfdfe7dc352907fc980b868725387e9854199f6f290212f5334886e8ce746065"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd47027a8f509c3f94785f9264aba1c7", "guid": "bfdfe7dc352907fc980b868725387e980c428b8fca5ba25a6de06e30860f2dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4fefb2a2de5e1fa64ea1eb2bb56250a", "guid": "bfdfe7dc352907fc980b868725387e98bd70ba9069512b41b1afd5986ce7a4f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823ebbe9de89c2c628365f1ea76536f53", "guid": "bfdfe7dc352907fc980b868725387e9815d94be25c8458309a22eec69efb9bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f72fccb11ad63f4043b14e37fbd4b1d6", "guid": "bfdfe7dc352907fc980b868725387e98efc1cb1c358678f0a65a7e061c030c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2f8cc4ab4d5990e5da5471619a6fee", "guid": "bfdfe7dc352907fc980b868725387e982f6dc5ee78f4079ce94e0bb4e0248f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f919f149851e31c8440b13a99db6e18", "guid": "bfdfe7dc352907fc980b868725387e98714e4f104563580e5eed132cf5b2d226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fd179b431810d63e79b14fc880d1c6", "guid": "bfdfe7dc352907fc980b868725387e98c4770df40799afb8f0af184c33a27f3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc11086f1443fbfae9729b3d66840ce0", "guid": "bfdfe7dc352907fc980b868725387e98727a7756d7685f493f7e96212ac1ee69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872431d61308c56f966bfc26613e6132e", "guid": "bfdfe7dc352907fc980b868725387e985711ecab61985e5c17d3b006043239dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a11f4f7962da9d31f7ceb508b392f7bf", "guid": "bfdfe7dc352907fc980b868725387e9890a2c925aeae73ddafd34bfc685bc000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db03d92369693f3e2dde61b44dfc8cd3", "guid": "bfdfe7dc352907fc980b868725387e985769b032f238c01ea413767d5c0d7d61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0a5d62aa7236f9bb19a1efe0c30c4cc", "guid": "bfdfe7dc352907fc980b868725387e98d65baa820cc386184c7cc8f5d8586b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864fa50eb57dbeae0841afc0bc47c5177", "guid": "bfdfe7dc352907fc980b868725387e98fe580fcd6d8ebbdb7b5e4ac180df5d4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98302d558e3edb3b87a2df027bfb6fea9a", "guid": "bfdfe7dc352907fc980b868725387e981b24d06e03420b5f1c4bba671a747193"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98781d3f3cd90de579268fd21afa9ee83c", "guid": "bfdfe7dc352907fc980b868725387e98251d91e42fb50eb51ee759368c39a927"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d52dfb666581ac6132793926b303d2", "guid": "bfdfe7dc352907fc980b868725387e98b1572c6cd3f509ebd416024633621269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee4b61d1eb29d09083c5c1047c66ee4", "guid": "bfdfe7dc352907fc980b868725387e98876f63207c2264ee04c2a53ad1278f4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d976acf40b111fa269d7e5381aff95c2", "guid": "bfdfe7dc352907fc980b868725387e9877ec46f402ebc6ffffd3ea378e2af1dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f676ba27ee4113254c97816e772fe448", "guid": "bfdfe7dc352907fc980b868725387e9842bbcbec480501ec40b186c8abecd864"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856643537e673e2b5b928bb80985fed75", "guid": "bfdfe7dc352907fc980b868725387e98533c7606df7d65be61afec5390dc1d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aec675c0c3316303156c1b1bea719913", "guid": "bfdfe7dc352907fc980b868725387e9804ae30782c79cba0c0c58b8f0a60a51b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f245c025324e0932f47078ce9ea09f", "guid": "bfdfe7dc352907fc980b868725387e98c2334be29ccd03054edc89c6f77838c2"}], "guid": "bfdfe7dc352907fc980b868725387e98be3a1575b59ab7d78b46f1b72ce8cedd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e9871e1b0bb3081df06fad9e4a23a366402"}], "guid": "bfdfe7dc352907fc980b868725387e98e400dd35304cc65c04efd018b59d1dad", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d300866f4a04d013e7bf2388451325f2", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e984aee925c667c62dbcbd8329688d99a19", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}