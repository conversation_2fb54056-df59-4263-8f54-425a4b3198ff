{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807b1768f8c6ccc2ec068f5008fb54aad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a57c98cd909b7c6e73d3a5e65e2cf3bf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980af4135041642bfb511cf32a532f101b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9803a80e3ff37f56d6112e3f1bd9a53d98", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980af4135041642bfb511cf32a532f101b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d7ce0c8ff440c746642822545a338c4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98132c1f698abe3739ce227591f6f3b320", "guid": "bfdfe7dc352907fc980b868725387e980f28d489d17b5c9c7eb243d31a9a584d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b3c0325121f1f737d1fd14fee3c223", "guid": "bfdfe7dc352907fc980b868725387e983cb25109aa03da56a1245d6a54798e10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eecb6dae9bd13e296be35d26dec09c48", "guid": "bfdfe7dc352907fc980b868725387e9828e355b1718caa05b766f57dee4ed1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a560c751bccb35bf3b46e4dd617bc7d", "guid": "bfdfe7dc352907fc980b868725387e98e078286d94f8faabe5cc101d3ec7b114", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa42f908dcdbf7120e3fb56da969e38", "guid": "bfdfe7dc352907fc980b868725387e983dbba96638b6bc7f335090205c2ca47f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ccfe27a969368cc05cb5bc3a7ee522", "guid": "bfdfe7dc352907fc980b868725387e9886529365268a7c4b6f3027e2bca279dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa082b1a347d36b6b77a039ff96c2a3", "guid": "bfdfe7dc352907fc980b868725387e98d1aa6d378c474062a4cc73bab68629da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88b0997ec319c7d6ee67782512e345c", "guid": "bfdfe7dc352907fc980b868725387e98460eef5d6f302856131f1146110f016d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884327fab1550f9af4e3005f164ebbee9", "guid": "bfdfe7dc352907fc980b868725387e98cd6f48333869d9690cf782bb8f2f1249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98925034961c30278e23a1c3a3a882f4c0", "guid": "bfdfe7dc352907fc980b868725387e98d080b38a2731dd61ef6de7d32c422dc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828554671dfd4af892997d8a3e5a3a631", "guid": "bfdfe7dc352907fc980b868725387e98b368c20fc3a80f4f67301b07cf3805e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a1dd6be9eac3d43a6cb30553eb9ef34", "guid": "bfdfe7dc352907fc980b868725387e98eb63bf9fe78cfa9d1aee9050d3ce0738", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9513842e7e180049f9960e37d1fea96", "guid": "bfdfe7dc352907fc980b868725387e98556c8a4bf1e504e96528b90ddb5f67cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881cde82a9fb720db20e1bbc2bef9d796", "guid": "bfdfe7dc352907fc980b868725387e988426fe8d3286cbd1d830972d4a06e459", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e3cbf6455c2cf2b9d543a1c13c65b82", "guid": "bfdfe7dc352907fc980b868725387e983010d3df0333c2b871822562c2656e39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a5b9e239811dba1f09816bf765f175", "guid": "bfdfe7dc352907fc980b868725387e98010efc535dccf439b3c4788165fa7a62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafde29f8d72766408496260d13007f7", "guid": "bfdfe7dc352907fc980b868725387e985e9d4886991e5ee4f09375d61a63c533", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989083a3f63f7d4736197af747df428dac", "guid": "bfdfe7dc352907fc980b868725387e98f124c681c5f28d7b321f56b0cdb74790", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29843c0e92ad63afc5a2dbda343ad06", "guid": "bfdfe7dc352907fc980b868725387e98299ee9d88f96000118bc29fad9cc22b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f216ce078d452ae044401a0fabadd1d9", "guid": "bfdfe7dc352907fc980b868725387e986574666f4dd04717b718302a3767123c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50235aabe882345f6af6682c891a140", "guid": "bfdfe7dc352907fc980b868725387e98e2abae9b4f65d8a2fbbe1cb4efb82d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd9485dd04847e58f75527af80e7fa22", "guid": "bfdfe7dc352907fc980b868725387e982f1f6f874ab05e8611167202e6bb3565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ffe1fc64db4c15b1304c9a105fd988b", "guid": "bfdfe7dc352907fc980b868725387e98e05dd000f9f26a08406de94e8012ab92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de16a120869ad6f1c6d5876da6bcb637", "guid": "bfdfe7dc352907fc980b868725387e98c74e9c6b51b59fb847f853dd946adffc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84599326da0767cd419f6c6199a4be9", "guid": "bfdfe7dc352907fc980b868725387e982c78dae076b494d4620b5b1528d31f4a"}], "guid": "bfdfe7dc352907fc980b868725387e98909723cc626a1303777f4927d91bf44a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98091fb00ced2179837a3a2963ead38d86", "guid": "bfdfe7dc352907fc980b868725387e98c3499d86dedd83203fdf570bbd876b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984048072597225ec1ef8c845a066f48aa", "guid": "bfdfe7dc352907fc980b868725387e98eb9de1a5d870bcda4a6891d81ed96c82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b4f6b796deafa5c45c749b978911ef9", "guid": "bfdfe7dc352907fc980b868725387e9832779b9f41ddc113cb9f1aa19afa93a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981582a9e717a9ab578ca76630c91f0202", "guid": "bfdfe7dc352907fc980b868725387e98a7b9609e06cbb458d2995d5dfa8c8c7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d2162773b2b96f15e3d4d6a3d127803", "guid": "bfdfe7dc352907fc980b868725387e980630d69799dbcbf1ceea74a54bae29a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc402d91f1a6505ce6742bb004eeb22", "guid": "bfdfe7dc352907fc980b868725387e98db955b1274d30b39328d2b61187e92c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ebfcfec0074aba2593abe0ec85c33b", "guid": "bfdfe7dc352907fc980b868725387e9878e32214376a5effbbc7098d2dae2db0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56e021409378d0ffdb001d3216f6eb7", "guid": "bfdfe7dc352907fc980b868725387e98870f9215a42fe3d44e481d04b48892e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfb48cad0d8f6b442c0580dd7831892d", "guid": "bfdfe7dc352907fc980b868725387e98b979d539b8ee9f8a82bf037abaebfbe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5daf12997a7664a7edfaededeeaaf2", "guid": "bfdfe7dc352907fc980b868725387e98eda8ba9b9541a3c4b81ade2ee86da8f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d45273fb43d4e4aad556e610966bf3", "guid": "bfdfe7dc352907fc980b868725387e986338ce7716d3f31c8f806a2c2d1d3cb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f102ae74ea85b5c75a565a05c5cd059", "guid": "bfdfe7dc352907fc980b868725387e9800535cd0cfc531008e5ff3fa9dc3eec3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988810fc6765328df38f93ec2de823b735", "guid": "bfdfe7dc352907fc980b868725387e981b8f2b5fb31e670c3477a44698740e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc93e7cdc58282b1878aaf2df583334", "guid": "bfdfe7dc352907fc980b868725387e98c4f1461f6d71aa5d225b20a95529a812"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb4b000cded606f85c0beeaea54aaae8", "guid": "bfdfe7dc352907fc980b868725387e9806db9ce7815408b8cb91f015d74a9038"}], "guid": "bfdfe7dc352907fc980b868725387e9875b31e9409e216860eb340489aa365a4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981851f400a3440227ea96cb082236b4c9", "guid": "bfdfe7dc352907fc980b868725387e98268cb2c1e147091e8e2f799ab61f19a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98316e3fc14c77f397e2bffb626e1f9142", "guid": "bfdfe7dc352907fc980b868725387e98114c7f9ccbf05c6b1670ae8b04b469a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532bd9654503d9cc12ec320147c6dc85", "guid": "bfdfe7dc352907fc980b868725387e988bbd449dabf095e5eee06d5fdda9918d"}], "guid": "bfdfe7dc352907fc980b868725387e988cd1dbd169658cce41cd989e806421a6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc166f694ca476a11a111c6a1cf3c5d5", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98add5731a2d7efd69941c31806e517423", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}