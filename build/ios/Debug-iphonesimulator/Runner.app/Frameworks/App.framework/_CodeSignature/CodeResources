<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		T7ae0s1yqPVdrLK0y5gqABVb1PU=
		</data>
		<key>flutter_assets/AssetManifest.bin</key>
		<data>
		ME0cAg6cl/bTZmwXEVgMugDccUI=
		</data>
		<key>flutter_assets/AssetManifest.json</key>
		<data>
		oG5/OGn+vw7vp/nu5DUDoZJ4nFc=
		</data>
		<key>flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>flutter_assets/NOTICES.Z</key>
		<data>
		5eJ9t0YtzWGBgtGweqY9kKcd7xE=
		</data>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>flutter_assets/isolate_snapshot_data</key>
		<data>
		Wk6mCKWZfp8fk4dScf84AwTD0WA=
		</data>
		<key>flutter_assets/kernel_blob.bin</key>
		<data>
		dnzq8DD25zbyQfJ7ZsNKYVzsGwU=
		</data>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>flutter_assets/vm_snapshot_data</key>
		<data>
		yyq5EcsmxYyBwFjTZrLTRBq4XWU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			AK9VrT1vIYmP534P8JLRoc2lLJQbaGDpko1FyK+MCV0=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sps95+7JukayebvM0TLjL1LW1VXXndpKp/O8tOm9ZR8=
			</data>
		</dict>
		<key>flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			oB7gd6EoWMJrskOU/S2GzSF89NF0/yWXkcibQxXjAXM=
			</data>
		</dict>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			7/uNIhK1V6BRnEWEtsGn8+eTWO17GC3x900r9VMcyp4=
			</data>
		</dict>
		<key>flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			TP9dsVwOhUfyX8HIZR0Vn739oWDGC/Xahdvnp6Bp6Wg=
			</data>
		</dict>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			9GUhMA+MB30KbahT/4tgci98x/CLK+R2R7dFrti99M8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
