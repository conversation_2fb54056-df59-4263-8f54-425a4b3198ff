<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		Xn4L7bM2rcO/4cI2SnVsh54DvCY=
		</data>
		<key><EMAIL></key>
		<data>
		nD8gCaNiuUwPKJcTt5DnmqkeK1o=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		j3AXBFyMHSuMA9yK6/3dOz7YUn8=
		</data>
		<key>Assets.car</key>
		<data>
		wsYGDJakVcV0g8FSV3a/0VoaIbI=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		jUF08zJeE4tN8zaCqLeoTEumwQQ=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		T7ae0s1yqPVdrLK0y5gqABVb1PU=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		L4m7s7twdw7M+R3wBH9tjNJnmvI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		ME0cAg6cl/bTZmwXEVgMugDccUI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		oG5/OGn+vw7vp/nu5DUDoZJ4nFc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		5eJ9t0YtzWGBgtGweqY9kKcd7xE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		Wk6mCKWZfp8fk4dScf84AwTD0WA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		+aTbdxOxkVhePJ9/ohSkOtwOaHg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		yyq5EcsmxYyBwFjTZrLTRBq4XWU=
		</data>
		<key>Frameworks/CwlCatchException.framework/CwlCatchException</key>
		<data>
		+qdx68uasYJIwPB6V9UruXT58Gc=
		</data>
		<key>Frameworks/CwlCatchException.framework/Info.plist</key>
		<data>
		EBgFkFe4sLOGc+oH6dUbCDMnE7E=
		</data>
		<key>Frameworks/CwlCatchException.framework/_CodeSignature/CodeResources</key>
		<data>
		uyXSHa8vEfa3p9+NGGLCJY+vkFk=
		</data>
		<key>Frameworks/CwlCatchExceptionSupport.framework/CwlCatchExceptionSupport</key>
		<data>
		ekVjkhgCEj77zYDWUo1YM+lXqsA=
		</data>
		<key>Frameworks/CwlCatchExceptionSupport.framework/Info.plist</key>
		<data>
		q9p2xazvXdGIyJuhvOHeAc3E3Kw=
		</data>
		<key>Frameworks/CwlCatchExceptionSupport.framework/_CodeSignature/CodeResources</key>
		<data>
		dxyQ9mkVibZJTXHaCIaM+s2WdUQ=
		</data>
		<key>Frameworks/FirebaseAppCheckInterop.framework/FirebaseAppCheckInterop</key>
		<data>
		RHGOE1GPeBLm0SnM1qrOi9xCnCY=
		</data>
		<key>Frameworks/FirebaseAppCheckInterop.framework/Info.plist</key>
		<data>
		xoJDOTYtFv2XPgo1RUNqM377630=
		</data>
		<key>Frameworks/FirebaseAppCheckInterop.framework/_CodeSignature/CodeResources</key>
		<data>
		9w9QQ7JNPlKdR7ZH9ybdCYcQ65w=
		</data>
		<key>Frameworks/FirebaseAuth.framework/FirebaseAuth</key>
		<data>
		fB9z6XEgbGFbaNrlAl2eGLX1o68=
		</data>
		<key>Frameworks/FirebaseAuth.framework/FirebaseAuth_Privacy.bundle/Info.plist</key>
		<data>
		3oMefHa9wgcDscOyGp41xm09Ms4=
		</data>
		<key>Frameworks/FirebaseAuth.framework/FirebaseAuth_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		8cQ/rSS7XR8ohTuSAR27mQb1wiU=
		</data>
		<key>Frameworks/FirebaseAuth.framework/Info.plist</key>
		<data>
		Iquogd9pARjIA+AscMXj1keIn7A=
		</data>
		<key>Frameworks/FirebaseAuth.framework/_CodeSignature/CodeResources</key>
		<data>
		STtmSCOLfU6UQ0ZJZxfOfAJFoDU=
		</data>
		<key>Frameworks/FirebaseAuthInterop.framework/FirebaseAuthInterop</key>
		<data>
		4lK6kCkQmIYVUfdmyjL9fe8XvD8=
		</data>
		<key>Frameworks/FirebaseAuthInterop.framework/Info.plist</key>
		<data>
		xCJetbtZ9WmUrMM8L7R2hGJTdRA=
		</data>
		<key>Frameworks/FirebaseAuthInterop.framework/_CodeSignature/CodeResources</key>
		<data>
		GsGLaErWV3BLS0eUGfCYUrRLrVk=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore</key>
		<data>
		qyGZTZVIeXHvT2eln49rdb3tOuo=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist</key>
		<data>
		23y1/dyRlcSwMdQ7+mPOSp59fCM=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sa2OhFlqdCIyz9oV7fUdDKWzFL0=
		</data>
		<key>Frameworks/FirebaseCore.framework/Info.plist</key>
		<data>
		oSocNEi1c7/uf75bOGM3Mvt9JzM=
		</data>
		<key>Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources</key>
		<data>
		hpe+3g/4mwPFARrDrFkDyy9MESE=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension</key>
		<data>
		Ek0U2SCNDynZD8ZM6v44aUE8c+I=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/Info.plist</key>
		<data>
		VXkn/v/oq8cQMbw2Ttpx/PDXXHM=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/Info.plist</key>
		<data>
		CXXWqQE7/NDMkxYnYqRM6b7e978=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/_CodeSignature/CodeResources</key>
		<data>
		bCYwCCOnJjV2lO8BxGVXZ7wBuRk=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal</key>
		<data>
		41WahbNmUISZazgFIaodtdGEfpw=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		SO00f5f40BzKa5+iX9DBq5cWYj4=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ifoThrqbbqoLG4yjAruMQRaf0Dw=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/Info.plist</key>
		<data>
		P6dPLptFi8SYapeI2cfxmj89wIQ=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources</key>
		<data>
		dkvA9ty71S9JdX4FfLCbSzwkeo4=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore</key>
		<data>
		NivdOXuxCrDLDzTkjSnGmMbao9w=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/Info.plist</key>
		<data>
		+BMq4AOMD8hPs/b083tNEHtKPUs=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/Info.plist</key>
		<data>
		umFAGb562Ocs5m1FTb/eF6WrZ5E=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/_CodeSignature/CodeResources</key>
		<data>
		7stAxwv02ky/4G117aI2Ghf/H+A=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal</key>
		<data>
		o5QPHvxHJZntmH8mAaM19l3HUk8=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		wPjcGT8XDZ+ByBipqpVtIjZePsk=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/Info.plist</key>
		<data>
		OXCz7JLG45JoDCRoj8rqxDUH2VY=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/_CodeSignature/CodeResources</key>
		<data>
		qCP8u+2pdOjxrxujhihl2cyw+0c=
		</data>
		<key>Frameworks/FirebaseSharedSwift.framework/FirebaseSharedSwift</key>
		<data>
		cmDcX2qbK9ACTi/E4MntcnNnhb0=
		</data>
		<key>Frameworks/FirebaseSharedSwift.framework/Info.plist</key>
		<data>
		U/6hoJ23uVYddIL9n6rGKfY5RGo=
		</data>
		<key>Frameworks/FirebaseSharedSwift.framework/_CodeSignature/CodeResources</key>
		<data>
		uaxTdBknG16k5MquYi2RKgeOH4M=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		Bn5yEzx+9B+GvylNn34fcPF/YBE=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		JjEDxhy8u4fIz3XvljshI4ewfe8=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		PfJCf6hbYTWm910ECDC5roRPfWE=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		D70rYh3aLTKGSkULlIIB2iSGgFQ=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		6j9UsEzwt5vXmRXHZ2LReDHjlC8=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterSceneDelegate.h</key>
		<data>
		PUjypOjKOl9y4SFZSiAYyBbejM0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		fl+WpzcDlRyqEw2UNn2zFJiFZAw=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		2kFoOl16SHHbyMqu8UjybH8D7JI=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher</key>
		<data>
		KbyjSobfTbQiNAwqer79tUi+Zhc=
		</data>
		<key>Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher_Core_Privacy.bundle/Info.plist</key>
		<data>
		PJeros8NEogX99EPCO+qnw8er8I=
		</data>
		<key>Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		GqeAMkwbcNQeG0K4qQhQh2vHhHo=
		</data>
		<key>Frameworks/GTMSessionFetcher.framework/Info.plist</key>
		<data>
		qFYTnswflkJeOn9KnafxJPe8Lms=
		</data>
		<key>Frameworks/GTMSessionFetcher.framework/_CodeSignature/CodeResources</key>
		<data>
		hVwRizG54T8HyAkhBYUFmC6Nxgs=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities</key>
		<data>
		eGYMGr2fAQ/OTJIhUOXrB95cp1w=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist</key>
		<data>
		v1ZlvlncGWoALemH9SeUUVhC1bE=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		9Dge7JFNlx7Vk430tsjNsK3d0Ng=
		</data>
		<key>Frameworks/GoogleUtilities.framework/Info.plist</key>
		<data>
		FaPrZW/K2rNe6yHFLj947wJoV3A=
		</data>
		<key>Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources</key>
		<data>
		ALCs6gziCUe4QJI5RKElgGQF1eU=
		</data>
		<key>Frameworks/RecaptchaInterop.framework/Info.plist</key>
		<data>
		QMg35Qmc0JY+W3VoMYw5KXy/QvY=
		</data>
		<key>Frameworks/RecaptchaInterop.framework/RecaptchaInterop</key>
		<data>
		7Xmd0Tb15HDCw+J+3YL84dQoN4g=
		</data>
		<key>Frameworks/RecaptchaInterop.framework/_CodeSignature/CodeResources</key>
		<data>
		GZQMG4kguHyvkR/U5LEQHS8WM08=
		</data>
		<key>Frameworks/absl.framework/Info.plist</key>
		<data>
		PlgmF5Gev8bgCVjiVQ4aEW8rvdU=
		</data>
		<key>Frameworks/absl.framework/_CodeSignature/CodeResources</key>
		<data>
		cDOoyyHpsDCYBrIb88/Zr6iCALk=
		</data>
		<key>Frameworks/absl.framework/absl</key>
		<data>
		NjOlb4UU6vJyeW7bAU58ZeKj+js=
		</data>
		<key>Frameworks/absl.framework/xcprivacy.bundle/Info.plist</key>
		<data>
		V+FOqrV6B9ltz4cJlRm07PHg4OA=
		</data>
		<key>Frameworks/absl.framework/xcprivacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		1GKSpq20OoFIxAuJBtHAw+vMZO8=
		</data>
		<key>Frameworks/audioplayers_darwin.framework/Info.plist</key>
		<data>
		8nPKntbeFafkvTbawZ1NJbos7j8=
		</data>
		<key>Frameworks/audioplayers_darwin.framework/_CodeSignature/CodeResources</key>
		<data>
		ZtcZEKsEjD6RPYSrjTmhNmkrI0c=
		</data>
		<key>Frameworks/audioplayers_darwin.framework/audioplayers_darwin</key>
		<data>
		wNpzmirnsv21ha4ft1v9f7pIbzw=
		</data>
		<key>Frameworks/grpc.framework/Info.plist</key>
		<data>
		ue0atJamtsX8Mpk30PPQVZlp5Qk=
		</data>
		<key>Frameworks/grpc.framework/_CodeSignature/CodeResources</key>
		<data>
		YiF7x8xYvleLx/6onlhKF+4eJvM=
		</data>
		<key>Frameworks/grpc.framework/grpc</key>
		<data>
		acxyZTMDYfBHYb8JTo85GwKSfF4=
		</data>
		<key>Frameworks/grpc.framework/grpc.bundle/Info.plist</key>
		<data>
		vMDffgnAOKIAP/QCRodU6xicg2A=
		</data>
		<key>Frameworks/grpc.framework/grpc.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/grpcpp.framework/Info.plist</key>
		<data>
		jctKQ92NvZLnno60tUFIW10xS4o=
		</data>
		<key>Frameworks/grpcpp.framework/_CodeSignature/CodeResources</key>
		<data>
		a3iOps5rYhkyTRwAXgv+lQcdtqw=
		</data>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/Info.plist</key>
		<data>
		mbX7HuqTqTVXm/QU2orlX0bYvAQ=
		</data>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/roots.pem</key>
		<data>
		fKPpDnXJArmBT4pB8zmpeWVIzCc=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp</key>
		<data>
		3BrK48AJlrcK8IWJbs3tEXvJkRE=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/Info.plist</key>
		<data>
		OatQErywvVIyWnO62zlU2XXQQT0=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/leveldb.framework/Info.plist</key>
		<data>
		O+uiJ1cDyIacInHBHHcw01sT+2c=
		</data>
		<key>Frameworks/leveldb.framework/_CodeSignature/CodeResources</key>
		<data>
		LSd+V0hjFfDbjwkUVau80cz3fFM=
		</data>
		<key>Frameworks/leveldb.framework/leveldb</key>
		<data>
		ELpb+joIixbtJlIu3SdEfLBmYLU=
		</data>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/Info.plist</key>
		<data>
		KBLFZZsIQpc9rYaDPNA0Sy+5Q9E=
		</data>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		JTGYQTSkuJ7uGfD1SfjQTq3ngyk=
		</data>
		<key>Frameworks/nanopb.framework/Info.plist</key>
		<data>
		UsM20kKAEBs85Xe9uQOPgSZZatY=
		</data>
		<key>Frameworks/nanopb.framework/_CodeSignature/CodeResources</key>
		<data>
		RGML7IfALY9GpsL557AvsH+Fl/s=
		</data>
		<key>Frameworks/nanopb.framework/nanopb</key>
		<data>
		ovWnim4T4QhRoU0qmHScr5gZ54k=
		</data>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist</key>
		<data>
		CU6hD/8YMVeVZCEAem9EJMxFGXg=
		</data>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KY5lfwC2TvsgFj4wt7hkMmainbs=
		</data>
		<key>Frameworks/openssl_grpc.framework/Info.plist</key>
		<data>
		G1vxeuEW1NVU66z1en9W8TmvuXI=
		</data>
		<key>Frameworks/openssl_grpc.framework/_CodeSignature/CodeResources</key>
		<data>
		QKXETfSUeRA4ywV0mEcNYcyxR4U=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc</key>
		<data>
		uZMtEOppzrstBg566Eth2wtufag=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/Info.plist</key>
		<data>
		n31wEKYFn0h2Y/HlcZc5omh/igQ=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		K90+CLnEOxShfGtDla3/qrlU8cA=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		xfwA4BuJAGlynMGv5n/JvKbayR8=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		MC+VtBUVhgcwFRyCCw+AJ6t+gDk=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		YTM1ZPS8mbjHh6rohYqJdKV0YDk=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		1TOp+vJW4ewE5gzeHpOD4y+t7Zc=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		rqZ5+dqJLnnhsZibXw2+Os04PSI=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		o6YhQtvDPfMsCK1kgLpjQogwErY=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		6MHqFGZehO0cL0+jPsrEvw4ouaY=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/speech_to_text.framework/Info.plist</key>
		<data>
		lDWQ5v9nWFnat9blMv+EjrjLVSw=
		</data>
		<key>Frameworks/speech_to_text.framework/_CodeSignature/CodeResources</key>
		<data>
		sbSj1l8Z8pvPCHKaeikxJTgC3y8=
		</data>
		<key>Frameworks/speech_to_text.framework/speech_to_text</key>
		<data>
		vVpJ5Q98ZPW9yb8Ita3xM2NvVtI=
		</data>
		<key>GoogleService-Info.plist</key>
		<data>
		lXEyEWg6nIkjaFd4DjcQZ5Xj3kk=
		</data>
		<key>Info.plist</key>
		<data>
		W1L+w4VHDdkuod/Ti6/tbfyZap4=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		uZyEX+SwvwsVKx99SOihf9vW2hk=
		</data>
		<key>__preview.dylib</key>
		<data>
		ne9OAckF8jTyX7nporhcbVRfR98=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			G4lBGUbG8Y+PUnHlv145+f60u99sUfvqFJdwYSD2rcM=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			egUE0NPTvOvrtjuOC+mvE2qnyC9/pwQj30cCjDYgQP4=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mWX/prDtmL5mSHSYPkM4HOiGAct4XJQyXnzADNoe7PM=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			MN1adm8pbCZNmOmSPQpUMFVNHzkOhpcEOhs8eSowE3g=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			7wbYK1XSnYtSBve057mGK9rRRN1x9yIQFoTuc83cD0c=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kbjTW9nIi0OuDqyDSPm1fS8+IJpFAOZRaxi0Wbfj8r4=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			cplgIPJH2DPuUfenYAI586NSZ52xL3Y8F4y0QYuB0Vg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			AK9VrT1vIYmP534P8JLRoc2lLJQbaGDpko1FyK+MCV0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sps95+7JukayebvM0TLjL1LW1VXXndpKp/O8tOm9ZR8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			oB7gd6EoWMJrskOU/S2GzSF89NF0/yWXkcibQxXjAXM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			7/uNIhK1V6BRnEWEtsGn8+eTWO17GC3x900r9VMcyp4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			qF1RAD+YtHW7T5Wfnroy4L2wVUALOMB9uMlNBPtyyxE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			9GUhMA+MB30KbahT/4tgci98x/CLK+R2R7dFrti99M8=
			</data>
		</dict>
		<key>Frameworks/CwlCatchException.framework/CwlCatchException</key>
		<dict>
			<key>hash2</key>
			<data>
			6XDOTHqtWqEq4BKKGZpj20crbcgnpieoG2j7luzD3SQ=
			</data>
		</dict>
		<key>Frameworks/CwlCatchException.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HRcWbzF/G2OGZrvFNUnFqIz+1J8utN/mLDRGkW5uyik=
			</data>
		</dict>
		<key>Frameworks/CwlCatchException.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			7CFiLOys9+HkA04luHrJk1C8b75BTU0KPKllFuhTbrE=
			</data>
		</dict>
		<key>Frameworks/CwlCatchExceptionSupport.framework/CwlCatchExceptionSupport</key>
		<dict>
			<key>hash2</key>
			<data>
			qOcYoS8KCN3Zs5C9W3If0+4VFu8gWQIUGcYHalzxSEg=
			</data>
		</dict>
		<key>Frameworks/CwlCatchExceptionSupport.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eKztslqpAsrQeCCX009EAvBqx1+THhs6IkRNIDLcfr0=
			</data>
		</dict>
		<key>Frameworks/CwlCatchExceptionSupport.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			kN4/4sLl11VJJy4SGTemC7neGVGeB3LhlH5zwnA1ojQ=
			</data>
		</dict>
		<key>Frameworks/FirebaseAppCheckInterop.framework/FirebaseAppCheckInterop</key>
		<dict>
			<key>hash2</key>
			<data>
			Ohne+4EG9IZeoZGNmstTh7YBhrcXU762SZ2mF22rTN0=
			</data>
		</dict>
		<key>Frameworks/FirebaseAppCheckInterop.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SryX1JFx5MR3YHlkgm3wPlTh7tnUpMPWDjneNUjS5T0=
			</data>
		</dict>
		<key>Frameworks/FirebaseAppCheckInterop.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			jOAnCJpBZiMjL6YQW+y44aKyNViB8SjC1hCLnLEuivc=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuth.framework/FirebaseAuth</key>
		<dict>
			<key>hash2</key>
			<data>
			N9q2n2weB9waClOwH/usVVUGjwa3d31Ee/nnjp3mafc=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuth.framework/FirebaseAuth_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Yh2AJKoteGcwMlPG24mkKzLSgs/J+ZsLT7g5b/utb8Q=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuth.framework/FirebaseAuth_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BMFi6vpJss7QZjIxqJzJ9Xk1BsgCtoSGcZtdoGCK79U=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuth.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tQx4E3knX6kguqc7jZNwTpRbA0CoEGnqlLnCg0zPVmQ=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuth.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			SNu3P53vUjiMQceT9S2XsN/W3MU7lL4t1Y7jeRGm1kk=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuthInterop.framework/FirebaseAuthInterop</key>
		<dict>
			<key>hash2</key>
			<data>
			Y4/QQYhCye4JFVa23pdL1MvEfzPKMPioiHmxBtSmZ2E=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuthInterop.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			35M89xyDJEvIcIDxxV5eumb1i5y9RphWb9Vd2XZHLp0=
			</data>
		</dict>
		<key>Frameworks/FirebaseAuthInterop.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			9yOtDHODPWU0Sq0CnlV64NUABoxi9QUyeYjVYcv90QA=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore</key>
		<dict>
			<key>hash2</key>
			<data>
			1PLE5OTpGmaGOFSjBuskcsSZp999Poi+MytEmsSNFPM=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			2yGaW6/Ss3bLpuARq2jt3/wo9yeeq4beS7PHy9QTnz8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			EeMfX2tg6A69WQFUn85QQ/mvPmg/h0AilFAAtAUwbD8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AUoTDeM0bpGFskR4P3ppt/T/+P7/ekdQDJptFfvBLbk=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			r7r6edSu77umXUPMtiYTPLnGcMCDwgs+ONZ687jLN8k=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension</key>
		<dict>
			<key>hash2</key>
			<data>
			FeW6Cp6wbviK8k6SIr+Kkzk36oOqCbsmfTZKIZ/JHhs=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SvPCTxlqHr+yOllOwtVI2CgpnWNo1wjfRuSCYZPFz/g=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+pGMzowPIJ1iQxqvgiF20qSJSZxOt4+fa/4GXE1zbho=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			as1c/WbOhK/77iq2d5u1I86qfgREtOI31kn3dU+hkVM=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal</key>
		<dict>
			<key>hash2</key>
			<data>
			tNgiyqV9e3dOzsD79xUEaJDKciGSvLJtXtZ+y5zvvh0=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			J+6Nfsa0aeC6ptzIMU0Rwdx6UHyzXm3nu6wFXGIqznk=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			W3/peUI97ePgivwppC8A9ghiddxUioTCl3QjWGPu0+8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/PXbdCaP1+t36cs1CIaR928WLyt4LtoXQeQ5kSaYet4=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ljUHpv7M2ZcymwamCsT0jqKIp47xaQTHGb5PK+HYp04=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore</key>
		<dict>
			<key>hash2</key>
			<data>
			X7mE1k8F+P8H2+YbnmNky4Jg8STiQuwtXFiolo3BsLA=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0BRflwCNycfQ8uIb6C1W6EWFjOHdyw+DYgfPfXjN1ZU=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			DLiynmV0kLymFci6UGeS3V2ntyepwZTVyVylKXMqGPw=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			oCVtYEbZferBDghhgJGyYYqchuPqXtFf8ErBBxl9/+o=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal</key>
		<dict>
			<key>hash2</key>
			<data>
			SqLwaA4YZB6Nh8fjzfr+AFVgJIQFn8X0hB1OR6Rgh3M=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/WtKtnXAzTXm6QGVig2ZeM0EJLJNxf7LOLE55PM+38c=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			D47hA/VcKdHeDzD9fi1Wx2OHLzYeOlZT3yL+tfGUdn0=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			rH/P81h43rWkuHiBEsNJdb48PJ9xE5ofEXBH96SHwWU=
			</data>
		</dict>
		<key>Frameworks/FirebaseSharedSwift.framework/FirebaseSharedSwift</key>
		<dict>
			<key>hash2</key>
			<data>
			Evorgh/H+ISVGeUY+84B9707RGg7yMJR3iRMFm8+gH0=
			</data>
		</dict>
		<key>Frameworks/FirebaseSharedSwift.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			4/pwG1YcMagFPy6In36871sM5JaIgnxDG9cFYja8d60=
			</data>
		</dict>
		<key>Frameworks/FirebaseSharedSwift.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			wFt7d8sQ271SXfysrxdM07iFYBEFZW19fJv3s+ZkkB8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			ktLoHxpM3xlmrMSJKfa9P8F1kRJe8ySvouT4G4JKWwU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			V+TMeH4UAxxuVPQOohW50NJQqVdSlkAiuyF2tCWlFtQ=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ehumZ2VCA9xOXBI/7gQunPmAgn9cJpiZKDS9p8XWqkY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Sgwd6p0o9TyQvVQAj7mhGxVUNryt/FcgMWQLlq/CX4A=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8bnX1BsH77Yd+oEVzl2UbvxXnnr01bzB9HFAWsZZhxY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterSceneDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XZ4FFWN3VEjhcYy2BmkEknlpKSoVQRh25vkFQgE5Gww=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			V3jTznW5v9sCQUU96viaMSqKBMfa9lp6/k081GhdK8g=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			fX+pGS1XEquKPilDWVPgKbaNCV+vaiesUcBjp8ipX9Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher</key>
		<dict>
			<key>hash2</key>
			<data>
			DhsoGzcQuPaTCLl1BBMR6QYMymooFGmlifHfSiivFAY=
			</data>
		</dict>
		<key>Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher_Core_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bjKKcWm8qi3gxpOQNCtjC0rU0gxfZans8xY2rk89kiQ=
			</data>
		</dict>
		<key>Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			PkqTy+hqzvfdfgY6KMhJmS9Vbn9SytxfN8HosOG1RoY=
			</data>
		</dict>
		<key>Frameworks/GTMSessionFetcher.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ekl/cx6f0Ig4/kwMqnPFdPoq+niOrKJ5qLTONmLe62s=
			</data>
		</dict>
		<key>Frameworks/GTMSessionFetcher.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			hjXVo0l+d4M+Cf8i+itOasc6CZiGYtBZak/nFt6+VWM=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities</key>
		<dict>
			<key>hash2</key>
			<data>
			Kt4/uGR5UwePSwhXz+zUIyWhPFoBOa6TScczDbJc9MA=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			loMpW/Ritpeudn1qWTevjM6earVHxgnoihyK+//zbzM=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			+Btc+PBDZicS7KnpeFdnJkzxkAJf5720l3cpbAaN5Tw=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			d7j5m1MHPLCABabrbXNK2KaWEVEhOFxj2DiWdCKnC9I=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			biGl/ApsJ6S6dxTnm7fzHnS3rI8VbmMlAJ0wyGMsZaw=
			</data>
		</dict>
		<key>Frameworks/RecaptchaInterop.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			8/BT10aHLmlQqiiRwFbLgIrFFjYhSQCzdV0TINtZJ/8=
			</data>
		</dict>
		<key>Frameworks/RecaptchaInterop.framework/RecaptchaInterop</key>
		<dict>
			<key>hash2</key>
			<data>
			4BlmBmlDULs2Ou7jABgkcDiMFHHOK9wjdML8yZ855og=
			</data>
		</dict>
		<key>Frameworks/RecaptchaInterop.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			mTObvMkn4qTAiptCYva9SD9JbDE4L5BJcyzwMsdgcj4=
			</data>
		</dict>
		<key>Frameworks/absl.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LMMc89dmFwPZoVl5cD0WtZf/nx5uUrAFsDUau5ri06E=
			</data>
		</dict>
		<key>Frameworks/absl.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			swBrmyZYmZGwuLAYrO3LkCs6eRS44uQLvShk1dzF8pk=
			</data>
		</dict>
		<key>Frameworks/absl.framework/absl</key>
		<dict>
			<key>hash2</key>
			<data>
			xGP7wCbhAF4l3DlHucOoh4AwsYPgFGrCaoZKIF5tP3s=
			</data>
		</dict>
		<key>Frameworks/absl.framework/xcprivacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ikuYB2R59weEn/7tKrAjTcYu4zfZ1lYDYAbuD7yTYT8=
			</data>
		</dict>
		<key>Frameworks/absl.framework/xcprivacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			8jIheunt8qtqVBoo7/UM/gUwPCtHVv/5XMpy7MvDuJg=
			</data>
		</dict>
		<key>Frameworks/audioplayers_darwin.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Trd5VVH1DEB8co2WJPl1/oHKNlVS7CECLfLWF53rPQI=
			</data>
		</dict>
		<key>Frameworks/audioplayers_darwin.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			q+cYckrrf5kbB1XZNSWe2Y4zaEZ79uk9/tsNB3fbQjU=
			</data>
		</dict>
		<key>Frameworks/audioplayers_darwin.framework/audioplayers_darwin</key>
		<dict>
			<key>hash2</key>
			<data>
			cI5b77Bvx1Cfhu+kn68lBpD3iuitJmlsXj5DisVQ14M=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			RTFGhwwQD25MIALex42eWwhE4SXPM90wn3iy0vuVGNY=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			HIPY+uQO9Jep98tQmmnQQQomZ+xGRR9tksjA8hLeFNk=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc</key>
		<dict>
			<key>hash2</key>
			<data>
			+xmxzqt8LZtSCBpunM2ODK+tP1cZr7GlOOZtPMd5HSc=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nvzU6KQWB82ElZg2YOjeBLoHt9oLJn+ypFt+iDII6tc=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			QrgxviyoUWd9mm30e3Mx+RMnm6OTX7semoJlJGTeBpY=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			bu/vTXdkn5LEtnm6XQdBBUyDzOcoT7POV1HjRs+OYIc=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SvseWJ+W0HykYoUFwpCHewjqEOE+4xHojUlZ3MRmeic=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/roots.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			lhQzRMSuEJWIElssQdXa9lSl+vxuI/rDf3uj0p2n53Y=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp</key>
		<dict>
			<key>hash2</key>
			<data>
			zDstZ1CJ1Nc1hOoydk7h/F6B66I4YJaY0N2kg3b8N0A=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kjkzmD7ZU1zK8vKmnxsNMJsn99cSxRlWS6+6+APEXg8=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			wFpEfouwS3imuhFLWkG+VL231Lg/NsXS2GSMpjHEgjM=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Q9pLlcCyVA+6YcLDn7t4JN1r+uooxNkG3zslOrewQe4=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/leveldb</key>
		<dict>
			<key>hash2</key>
			<data>
			X++kxMlddid4L9hYWnSe3Ca6dKgLPIiF4JnURQpe1Mg=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CDfFH4qTURIsqaq5uU2KVHIBv2YVsVQWcynUhxqV0Xk=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CsAKC37/AHnc4lLimrQdcc9YxslP5OROoRDO9cYpcOE=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Z6idpovpW36m6+dDsyEitR5ja0PL/28UzJEsrBYxbrw=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Ghueo+HJoCqzSONkuKDDU4rlI1saEVlgkbaXWJ3HE1Q=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb</key>
		<dict>
			<key>hash2</key>
			<data>
			dQ1wV96d9YLX0qfIMAWvUOlwW8q6CEHiPpFDUcu/4p8=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			cKCNpPZMqGm3tEYf9+rYNoenelpDUgBBovHCgCmB418=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			cpujy9D0WMeM1h7fFzUO2v4ONMqG4xTsZMjLIszSG1Q=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			BZYOu8U450a4e1kwRnHIRSp/2lOSTiQev6Hm3My73ls=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			jN+KJ309VuQxrbKxE+7bxu0RTum3N55f3Y4R4Wxm9zI=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc</key>
		<dict>
			<key>hash2</key>
			<data>
			FJ8ZTmSwtvEPGAIkzecUBf7pmRGEtn8bvO9KW+KMpEg=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ZeFWBzCgMjVrCtSmmmZwxxw34EcfFpAYEvhf7Ms32T8=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			gqL8pZ/2TdQbpRjOv4deHA8sfudtAFsAjD1fKWgnNXc=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			dv7Y23rvmP8Ikiy9wPZDtU4gapxFHVQv/WaT359ZJzo=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			novsvaS63Kswqzx8BxujXj/wlcuZafAm03/xZ0w+JZY=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ON+sdvH+VEkM0nmEoqKPKE27Ubqed2efOBGn8r4q+Dw=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			GdoxVPBp6cwFkAvqHLSVx9+cJ9/Vy35u/ygXb7UacI8=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			3pJFEkZblQ+0Bzx6guPjnYORX8dPcqFnATkpywuAwiU=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			ceHRuTfkeC/P32IfyZVSrkcAoTqdWIim5FbEkME6oRk=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eB6CYB1UZpkh8D5YdaY8dP65Z/vnn/3djlhkmIh7Yz4=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/speech_to_text.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eT4LpS0pIVRziKpb2D7BnABJajv6TE1gCbDk/B7oHjA=
			</data>
		</dict>
		<key>Frameworks/speech_to_text.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			B1j2JG5am7vNdAVRh17xPdtEHuDiO5tg5tyk+BTjNy0=
			</data>
		</dict>
		<key>Frameworks/speech_to_text.framework/speech_to_text</key>
		<dict>
			<key>hash2</key>
			<data>
			5YML12/NSO9VYGKEU/ZHQjgFKdyO2yVelejobUQXxmk=
			</data>
		</dict>
		<key>GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			M/+sNNUxjxv3G94zH9+V2OTabXw8KVuwqiZSXF+zaAw=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			DKJr1jiGHXJjsxzgh3gXyzGcanWZKhSkF92PmUVWkd8=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			j+bo6ZSs4Spxy9zVDUcrCRXEtm+NmGB4Zfch3IothkA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
